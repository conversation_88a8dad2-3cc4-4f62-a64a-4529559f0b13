/**
 * Architecture Test Script for Live YT Music Lyrics Extension
 * 
 * This script validates the new coordinator-based architecture by testing:
 * - Background coordinator functionality
 * - Content script communication
 * - Popup controller integration
 * - Settings synchronization
 * - Theme color coordination
 * - Multi-tab session management
 * 
 * <AUTHOR> Acharya
 * @version 3.0.0
 */

'use strict';

// ============================================================================
// TEST CONFIGURATION
// ============================================================================

const TEST_CONFIG = {
    TIMEOUT: 5000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    
    // Test URLs
    YOUTUBE_MUSIC_URL: 'https://music.youtube.com',
    
    // Expected components
    REQUIRED_COMPONENTS: [
        'background.js',
        'content.js', 
        'popup.js',
        'popup.html'
    ],
    
    // Test scenarios
    SCENARIOS: {
        SINGLE_TAB: 'single_tab',
        MULTI_TAB: 'multi_tab',
        SETTINGS_SYNC: 'settings_sync',
        THEME_COORDINATION: 'theme_coordination',
        SESSION_MANAGEMENT: 'session_management'
    }
};

// ============================================================================
// TEST RUNNER CLASS
// ============================================================================

class ArchitectureTestRunner {
    constructor() {
        this.testResults = [];
        this.currentTest = null;
        this.startTime = null;
    }

    /**
     * Run all architecture tests
     */
    async runAllTests() {
        console.log('🧪 Starting Architecture Tests for Live YT Music Lyrics Extension');
        this.startTime = Date.now();

        try {
            // Phase 1: Component Validation
            await this.testComponentsExist();
            
            // Phase 2: Background Coordinator Tests
            await this.testBackgroundCoordinator();
            
            // Phase 3: Content Script Tests
            await this.testContentScriptCommunication();
            
            // Phase 4: Popup Controller Tests
            await this.testPopupController();
            
            // Phase 5: Settings Synchronization Tests
            await this.testSettingsSync();
            
            // Phase 6: Theme Color Coordination Tests
            await this.testThemeCoordination();
            
            // Phase 7: Multi-tab Session Management Tests
            await this.testMultiTabSessions();
            
            // Generate test report
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
            this.recordTestResult('SUITE_FAILURE', false, error.message);
        }
    }

    /**
     * Test that all required components exist
     */
    async testComponentsExist() {
        this.currentTest = 'Component Validation';
        console.log('🔍 Testing component existence...');

        try {
            // Check if extension is loaded
            const extensionId = chrome.runtime.id;
            if (!extensionId) {
                throw new Error('Extension not loaded');
            }

            // Check manifest
            const manifest = chrome.runtime.getManifest();
            if (!manifest) {
                throw new Error('Manifest not accessible');
            }

            // Validate required scripts
            const backgroundScript = manifest.background?.service_worker;
            const contentScripts = manifest.content_scripts;
            
            if (!backgroundScript) {
                throw new Error('Background script not defined in manifest');
            }
            
            if (!contentScripts || contentScripts.length === 0) {
                throw new Error('Content scripts not defined in manifest');
            }

            this.recordTestResult('Component Validation', true, 'All components exist');
            
        } catch (error) {
            this.recordTestResult('Component Validation', false, error.message);
        }
    }

    /**
     * Test background coordinator functionality
     */
    async testBackgroundCoordinator() {
        this.currentTest = 'Background Coordinator';
        console.log('🔍 Testing background coordinator...');

        try {
            // Test coordinator initialization
            const response = await this.sendMessageToBackground('getActiveSessions');
            
            if (!response || typeof response !== 'object') {
                throw new Error('Background coordinator not responding');
            }

            // Test session management
            if (!Array.isArray(response.sessions)) {
                throw new Error('Session management not working');
            }

            this.recordTestResult('Background Coordinator', true, 'Coordinator responding correctly');
            
        } catch (error) {
            this.recordTestResult('Background Coordinator', false, error.message);
        }
    }

    /**
     * Test content script communication
     */
    async testContentScriptCommunication() {
        this.currentTest = 'Content Script Communication';
        console.log('🔍 Testing content script communication...');

        try {
            // Get active YouTube Music tabs
            const tabs = await this.getYouTubeMusicTabs();
            
            if (tabs.length === 0) {
                console.warn('⚠️ No YouTube Music tabs found - skipping content script tests');
                this.recordTestResult('Content Script Communication', true, 'Skipped - no YT Music tabs');
                return;
            }

            // Test content script health check
            const tab = tabs[0];
            const healthResponse = await this.sendMessageToTab(tab.id, {
                action: 'healthCheck'
            });

            if (!healthResponse || !healthResponse.status) {
                throw new Error('Content script health check failed');
            }

            // Test session state retrieval
            const stateResponse = await this.sendMessageToTab(tab.id, {
                action: 'getSessionState'
            });

            if (!stateResponse || !stateResponse.success) {
                throw new Error('Session state retrieval failed');
            }

            this.recordTestResult('Content Script Communication', true, 'Content script responding correctly');
            
        } catch (error) {
            this.recordTestResult('Content Script Communication', false, error.message);
        }
    }

    /**
     * Test popup controller functionality
     */
    async testPopupController() {
        this.currentTest = 'Popup Controller';
        console.log('🔍 Testing popup controller...');

        try {
            // Test popup settings
            const settingsResponse = await this.sendMessageToBackground('getSettings', {
                scope: 'popup'
            });

            if (!settingsResponse || !settingsResponse.settings) {
                throw new Error('Popup settings not accessible');
            }

            // Validate settings structure
            const settings = settingsResponse.settings;
            const requiredPopupSettings = ['popupFontSize', 'openTabBehavior'];
            
            for (const setting of requiredPopupSettings) {
                if (!(setting in settings)) {
                    throw new Error(`Missing popup setting: ${setting}`);
                }
            }

            this.recordTestResult('Popup Controller', true, 'Popup controller working correctly');
            
        } catch (error) {
            this.recordTestResult('Popup Controller', false, error.message);
        }
    }

    /**
     * Test settings synchronization
     */
    async testSettingsSync() {
        this.currentTest = 'Settings Synchronization';
        console.log('🔍 Testing settings synchronization...');

        try {
            // Test global settings
            const globalResponse = await this.sendMessageToBackground('getSettings', {
                scope: 'global'
            });

            if (!globalResponse || !globalResponse.settings) {
                throw new Error('Global settings not accessible');
            }

            // Validate settings structure
            const settings = globalResponse.settings;
            const requiredSettings = ['SYNC_OFFSET', 'AUTO_SCROLL', 'FONT_SIZE', 'HIGHLIGHT_COLOR', 'DYNAMIC_THEME_COLORS'];
            
            for (const setting of requiredSettings) {
                if (!(setting in settings)) {
                    throw new Error(`Missing global setting: ${setting}`);
                }
            }

            // Test settings update
            const testSettings = { FONT_SIZE: 25 };
            const updateResponse = await this.sendMessageToBackground('updateSettings', {
                settings: testSettings,
                scope: 'global'
            });

            if (!updateResponse || !updateResponse.success) {
                throw new Error('Settings update failed');
            }

            this.recordTestResult('Settings Synchronization', true, 'Settings sync working correctly');
            
        } catch (error) {
            this.recordTestResult('Settings Synchronization', false, error.message);
        }
    }

    /**
     * Test theme color coordination
     */
    async testThemeCoordination() {
        this.currentTest = 'Theme Color Coordination';
        console.log('🔍 Testing theme color coordination...');

        try {
            // Get active sessions to check theme color data
            const sessionsResponse = await this.sendMessageToBackground('getActiveSessions');
            
            if (!sessionsResponse || !sessionsResponse.sessions) {
                throw new Error('Cannot access sessions for theme testing');
            }

            const sessions = sessionsResponse.sessions;
            
            // Check if sessions include theme color data
            let hasThemeData = false;
            for (const session of sessions) {
                if (session.themeColor) {
                    hasThemeData = true;
                    
                    // Validate theme color format
                    if (!/^#[0-9A-F]{6}$/i.test(session.themeColor)) {
                        throw new Error('Invalid theme color format');
                    }
                }
            }

            this.recordTestResult('Theme Color Coordination', true, 
                hasThemeData ? 'Theme coordination working' : 'No theme data (expected if no music playing)');
            
        } catch (error) {
            this.recordTestResult('Theme Color Coordination', false, error.message);
        }
    }

    /**
     * Test multi-tab session management
     */
    async testMultiTabSessions() {
        this.currentTest = 'Multi-tab Session Management';
        console.log('🔍 Testing multi-tab session management...');

        try {
            const tabs = await this.getYouTubeMusicTabs();
            
            if (tabs.length === 0) {
                console.warn('⚠️ No YouTube Music tabs found - skipping multi-tab tests');
                this.recordTestResult('Multi-tab Session Management', true, 'Skipped - no YT Music tabs');
                return;
            }

            // Test session tracking
            const sessionsResponse = await this.sendMessageToBackground('getActiveSessions');
            
            if (!sessionsResponse || !sessionsResponse.sessions) {
                throw new Error('Session tracking not working');
            }

            const sessions = sessionsResponse.sessions;
            
            // Verify session count matches tab count
            const activeSessions = sessions.filter(s => tabs.some(t => t.id === s.tabId));
            
            if (activeSessions.length !== tabs.length) {
                console.warn(`⚠️ Session count (${activeSessions.length}) doesn't match tab count (${tabs.length})`);
            }

            this.recordTestResult('Multi-tab Session Management', true, 
                `Managing ${activeSessions.length} sessions across ${tabs.length} tabs`);
            
        } catch (error) {
            this.recordTestResult('Multi-tab Session Management', false, error.message);
        }
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Send message to background script
     */
    async sendMessageToBackground(action, data = {}) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Background message timeout'));
            }, TEST_CONFIG.TIMEOUT);

            chrome.runtime.sendMessage({ action, data }, (response) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Send message to content script in tab
     */
    async sendMessageToTab(tabId, message) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Tab message timeout'));
            }, TEST_CONFIG.TIMEOUT);

            chrome.tabs.sendMessage(tabId, message, (response) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Get YouTube Music tabs
     */
    async getYouTubeMusicTabs() {
        return new Promise((resolve) => {
            chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
                resolve(tabs || []);
            });
        });
    }

    /**
     * Record test result
     */
    recordTestResult(testName, passed, message) {
        const result = {
            test: testName,
            passed,
            message,
            timestamp: Date.now()
        };
        
        this.testResults.push(result);
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n🧪 ============================================================================');
        console.log('📊 ARCHITECTURE TEST REPORT');
        console.log('🧪 ============================================================================');
        console.log(`⏱️  Duration: ${duration}ms`);
        console.log(`📈 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log('\n📋 Test Details:');
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test}: ${result.message}`);
        });
        
        console.log('\n🧪 ============================================================================');
        
        if (failedTests === 0) {
            console.log('🎉 ALL TESTS PASSED! The new architecture is working correctly.');
        } else {
            console.log(`⚠️  ${failedTests} test(s) failed. Please review the issues above.`);
        }
        
        console.log('🧪 ============================================================================\n');
    }
}

// ============================================================================
// TEST EXECUTION
// ============================================================================

// Auto-run tests when script is loaded
if (typeof chrome !== 'undefined' && chrome.runtime) {
    const testRunner = new ArchitectureTestRunner();
    testRunner.runAllTests().catch(error => {
        console.error('❌ Test execution failed:', error);
    });
} else {
    console.warn('⚠️ Chrome extension APIs not available - tests cannot run');
}

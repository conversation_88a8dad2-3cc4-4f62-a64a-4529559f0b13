/**
 * Live YT Music Lyrics - Background Script Coordinator
 *
 * Central coordinator that manages communication between content scripts and popup,
 * handles session registry, lyrics fetching, and settings synchronization.
 *
 * New Architecture:
 * - Content scripts are single source of truth for their sessions
 * - Background script coordinates between multiple sessions
 * - Popup acts as pure controller/display interface
 *
 * <AUTHOR> Acharya
 * @version 2.0.0
 * @license MIT
 */

'use strict';

// ============================================================================
// CONFIGURATION
// ============================================================================

const COORDINATOR_CONFIG = Object.freeze({
    // Session management
    SESSION: {
        HEARTBEAT_INTERVAL: 2000,    // How often content scripts send heartbeats
        SESSION_TIMEOUT: 10000,      // When to consider a session dead
        CLEANUP_INTERVAL: 30000,     // How often to clean up dead sessions
        MAX_SESSIONS: 10             // Maximum number of tracked sessions
    },

    // Message routing
    MESSAGING: {
        POPUP_RESPONSE_TIMEOUT: 5000,
        CONTENT_RESPONSE_TIMEOUT: 10000,
        RETRY_ATTEMPTS: 2,
        RETRY_DELAY: 1000
    },

    // Settings storage keys (unified format)
    STORAGE: {
        GLOBAL_SETTINGS: 'ytmusic_global_settings',
        SONG_SETTINGS: 'ytmusic_song_settings',
        POPUP_SETTINGS: 'ytmusic_popup_settings',
        EXTENSION_ENABLED: 'ytmusic_extension_enabled'
    },

    // Default settings (matching content.js exactly)
    DEFAULTS: {
        // Content script settings (must match CONTENT_CONFIG.SETTINGS.DEFAULTS exactly)
        SYNC_OFFSET: 0,           // seconds to offset lyrics sync
        AUTO_SCROLL: true,        // enable auto-scroll
        FONT_SIZE: 20,           // font size in px
        HIGHLIGHT_COLOR: '#ff0000', // active line highlight color
        DYNAMIC_THEME_COLORS: false // enable dynamic theme colors from album artwork
    },

    // Popup-specific settings (separate from content script settings)
    POPUP_DEFAULTS: {
        popupFontSize: 13,
        openTabBehavior: 'tab'
    },

    // API Configuration (kept from original)
    APIS: Object.freeze({
        LRCLIB: Object.freeze({
            BASE_URL: 'https://lrclib.net/api',
            TIMEOUT: 10000,
            MAX_RETRIES: 2,
            USER_AGENT: 'Live YT Music Lyrics v2.0.0 (https://github.com/sukarth/liveytmusiclyrics)'
        }),
        LYRICS_OVH: Object.freeze({
            BASE_URL: 'https://api.lyrics.ovh/v1',
            TIMEOUT: 5000,
            MAX_RETRIES: 1,
            USER_AGENT: 'Mozilla/5.0 (compatible; Live-YT-Music-Lyrics/2.0)'
        })
    }),

    // Performance settings
    PERFORMANCE: Object.freeze({
        CACHE_TTL: 24 * 60 * 60 * 1000, // 24 hours
        MAX_CACHE_SIZE: 100,
        DEBOUNCE_DELAY: 300
    }),

    // Error messages
    ERROR_MESSAGES: Object.freeze({
        NO_INTERNET: 'No internet connection. Please check your connection and try again.',
        NETWORK_ERROR: 'Unable to connect to lyrics services. Please check your internet connection and try again.',
        TIMEOUT: 'Lyrics search timed out. Please try again in a moment.',
        RATE_LIMITED: 'The lyrics service is busy. Please wait a moment and try again.',
        SERVER_ERROR: 'The lyrics service is temporarily unavailable. Please try again later.',
        INVALID_SONG_INFO: 'Song information is incomplete. Please try playing a different song.',
        NO_LYRICS_FOUND: 'No lyrics found for this song. Try playing a different version of the song, or check back later.',
        GENERIC_ERROR: 'Unable to fetch lyrics at the moment. Please try again later.',
        NO_ACTIVE_SESSIONS: 'No active YouTube Music sessions found. Please open YouTube Music and play a song.',
        SESSION_NOT_FOUND: 'Music session not found. Please refresh the page and try again.'
    }),

    // Scoring weights for lyrics matching
    SCORING: Object.freeze({
        TITLE_WEIGHT: 35,
        ARTIST_WEIGHT: 25,
        DURATION_WEIGHT: 20,
        SYNCED_WEIGHT: 15,
        KEYWORDS_WEIGHT: 3,
        POSITION_WEIGHT: 2
    }),

    // Keywords for matching
    SPECIAL_KEYWORDS: Object.freeze([
        'radio edit', 'radio version', 'radio mix',
        'extended mix', 'extended version', 'extended',
        'club mix', 'club version',
        'original mix', 'original version',
        'acoustic', 'acoustic version',
        'live', 'live version',
        'remix', 'remaster', 'remastered'
    ]),

    EXPLICIT_KEYWORDS: Object.freeze(['explicit', 'clean', 'censored']),
    ATTRIBUTION_FILTERS: Object.freeze(['paroles de', 'lyrics by', 'courtesy of'])
});

// ============================================================================
// MAIN COORDINATOR CLASS
// ============================================================================

/**
 * Central coordinator that manages all communication between content scripts and popup.
 * Handles session registry, message routing, settings sync, and lyrics fetching.
 *
 * @class BackgroundCoordinator
 */
class BackgroundCoordinator {

    /**
     * Initialize the coordinator with session management and message routing
     */
    constructor() {
        // Session registry - tracks all active YouTube Music sessions
        this.sessions = new Map(); // tabId -> sessionData
        this.activeSessionId = null; // Currently focused session

        // Lyrics service components (kept from original)
        this.lyricsCache = new Map();
        this.requestQueue = new Map();

        // Message routing
        this.popupPort = null;
        this.messageHandlers = new Map();

        // Performance metrics
        this.metrics = {
            sessions: 0,
            messages: 0,
            lyricsRequests: 0,
            errors: 0
        };

        this.initializeCoordinator();
    }

    /**
     * Initialize all coordinator components
     * @private
     */
    initializeCoordinator() {
        try {
            this.setupMessageHandlers();
            this.setupSessionCleanup();
            this.setupLyricsCache();
            this.loadGlobalSettings();

            console.log('🎵 Background Coordinator initialized');
        } catch (error) {
            console.error('❌ Coordinator initialization failed:', error);
        }
    }

    /**
     * Set up message handlers for different types of communication
     * @private
     */
    setupMessageHandlers() {
        // Main message listener
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            return this.handleMessage(message, sender, sendResponse);
        });

        // Port connections for real-time communication
        chrome.runtime.onConnect.addListener((port) => {
            this.handlePortConnection(port);
        });

        // Tab events
        chrome.tabs.onRemoved.addListener((tabId) => {
            this.handleTabClosed(tabId);
        });

        console.log('🎵 Message handlers set up');
    }

    /**
     * Handle incoming messages and route them appropriately
     * @param {Object} message - The message object
     * @param {Object} sender - Message sender info
     * @param {Function} sendResponse - Response callback
     * @returns {boolean} True if response will be sent asynchronously
     * @private
     */
    handleMessage(message, sender, sendResponse) {
        try {
            const { action, data } = message;

            switch (action) {
                // Content script messages
                case 'sessionHeartbeat':
                    this.handleSessionHeartbeat(sender.tab.id, data);
                    sendResponse({ success: true });
                    return false;

                case 'sessionUpdate':
                    this.handleSessionUpdate(sender.tab.id, data);
                    this.broadcastToPopup('sessionUpdate', { tabId: sender.tab.id, ...data });
                    sendResponse({ success: true });
                    return false;

                case 'fetchLyrics':
                    this.handleLyricsRequest(data, sendResponse);
                    return true; // Async response

                // Popup messages
                case 'getActiveSessions':
                    this.handleGetActiveSessions(sendResponse);
                    return false;

                case 'setActiveSession':
                    this.handleSetActiveSession(data.tabId, sendResponse);
                    return false;

                case 'sendToSession':
                    this.handleSendToSession(data.tabId, data.message, sendResponse);
                    return true; // Async response

                case 'updateSettings':
                    this.handleUpdateSettings(data, sendResponse);
                    return true; // Async response

                case 'getSettings':
                    this.handleGetSettings(data, sendResponse);
                    return true; // Async response

                default:
                    console.warn('🔶 Unknown message action:', action);
                    sendResponse({ error: 'Unknown action' });
                    return false;
            }
        } catch (error) {
            console.error('❌ Error handling message:', error);
            sendResponse({ error: error.message });
            return false;
        }
    }

    /**
     * Handle port connections for real-time communication
     * @param {chrome.runtime.Port} port - The connected port
     * @private
     */
    handlePortConnection(port) {
        if (port.name === 'popup') {
            this.popupPort = port;

            port.onDisconnect.addListener(() => {
                this.popupPort = null;
                console.log('🎵 Popup disconnected');
            });

            // Send initial session list
            this.sendToPopup('initialSessions', {
                sessions: this.getSessionsList(),
                activeSessionId: this.activeSessionId
            });

            console.log('🎵 Popup connected');
        }
    }

    /**
     * Handle tab closed event
     * @param {number} tabId - The closed tab ID
     * @private
     */
    handleTabClosed(tabId) {
        if (this.sessions.has(tabId)) {
            console.log(`🎵 Session ${tabId} closed`);
            this.sessions.delete(tabId);

            // Update active session if needed
            if (this.activeSessionId === tabId) {
                this.selectNewActiveSession();
            }

            // Notify popup
            this.broadcastToPopup('sessionClosed', { tabId });
        }
    }

    // ============================================================================
    // SESSION MANAGEMENT
    // ============================================================================

    /**
     * Handle session heartbeat from content script
     * @param {number} tabId - Tab ID
     * @param {Object} data - Session data
     * @private
     */
    handleSessionHeartbeat(tabId, data) {
        const now = Date.now();

        if (!this.sessions.has(tabId)) {
            // New session
            console.log(`🎵 New session registered: ${tabId}`);
            this.sessions.set(tabId, {
                tabId,
                lastHeartbeat: now,
                isActive: false,
                ...data
            });

            // Set as active if no active session
            if (!this.activeSessionId) {
                this.activeSessionId = tabId;
                this.sessions.get(tabId).isActive = true;
            }

            // Notify popup of new session
            this.broadcastToPopup('newSession', { tabId, ...data });
        } else {
            // Update existing session
            const session = this.sessions.get(tabId);
            session.lastHeartbeat = now;
            Object.assign(session, data);
        }
    }

    /**
     * Handle session update from content script
     * @param {number} tabId - Tab ID
     * @param {Object} data - Updated session data
     * @private
     */
    handleSessionUpdate(tabId, data) {
        if (this.sessions.has(tabId)) {
            const session = this.sessions.get(tabId);
            Object.assign(session, data, { lastHeartbeat: Date.now() });
            console.log(`🎵 Session ${tabId} updated:`, data.type || 'general update');
        }
    }

    /**
     * Get list of active sessions for popup
     * @returns {Array} List of session data
     * @private
     */
    getSessionsList() {
        return Array.from(this.sessions.values())
            .filter(session => this.isSessionAlive(session))
            .map(session => ({
                tabId: session.tabId,
                song: session.song,
                playbackState: session.playbackState,
                isActive: session.isActive,
                themeColor: session.themeColor,
                lastUpdate: session.lastHeartbeat
            }));
    }

    /**
     * Check if a session is still alive
     * @param {Object} session - Session object
     * @returns {boolean} True if session is alive
     * @private
     */
    isSessionAlive(session) {
        return (Date.now() - session.lastHeartbeat) < COORDINATOR_CONFIG.SESSION.SESSION_TIMEOUT;
    }

    /**
     * Select a new active session when current one becomes unavailable
     * @private
     */
    selectNewActiveSession() {
        const aliveSessions = Array.from(this.sessions.values())
            .filter(session => this.isSessionAlive(session));

        if (aliveSessions.length > 0) {
            // Prefer playing sessions, then most recently updated
            aliveSessions.sort((a, b) => {
                if (a.playbackState?.isPlaying && !b.playbackState?.isPlaying) return -1;
                if (!a.playbackState?.isPlaying && b.playbackState?.isPlaying) return 1;
                return b.lastHeartbeat - a.lastHeartbeat;
            });

            const newActive = aliveSessions[0];
            this.setActiveSession(newActive.tabId);
        } else {
            this.activeSessionId = null;
        }
    }

    /**
     * Set active session
     * @param {number} tabId - Tab ID to set as active
     * @private
     */
    setActiveSession(tabId) {
        // Clear previous active
        if (this.activeSessionId && this.sessions.has(this.activeSessionId)) {
            this.sessions.get(this.activeSessionId).isActive = false;
        }

        // Set new active
        if (this.sessions.has(tabId)) {
            this.activeSessionId = tabId;
            this.sessions.get(tabId).isActive = true;
            console.log(`🎵 Active session set to: ${tabId}`);
        }
    }

    /**
     * Clean up dead sessions periodically
     * @private
     */
    setupSessionCleanup() {
        setInterval(() => {
            const deadSessions = [];

            for (const [tabId, session] of this.sessions.entries()) {
                if (!this.isSessionAlive(session)) {
                    deadSessions.push(tabId);
                }
            }

            deadSessions.forEach(tabId => {
                console.log(`🎵 Cleaning up dead session: ${tabId}`);
                this.sessions.delete(tabId);

                if (this.activeSessionId === tabId) {
                    this.selectNewActiveSession();
                }
            });

            if (deadSessions.length > 0) {
                this.broadcastToPopup('sessionsUpdated', {
                    sessions: this.getSessionsList(),
                    activeSessionId: this.activeSessionId
                });
            }
        }, COORDINATOR_CONFIG.SESSION.CLEANUP_INTERVAL);
    }

    // ============================================================================
    // POPUP COMMUNICATION
    // ============================================================================

    /**
     * Handle get active sessions request from popup
     * @param {Function} sendResponse - Response callback
     * @private
     */
    handleGetActiveSessions(sendResponse) {
        sendResponse({
            sessions: this.getSessionsList(),
            activeSessionId: this.activeSessionId
        });
    }

    /**
     * Handle set active session request from popup
     * @param {number} tabId - Tab ID to set as active
     * @param {Function} sendResponse - Response callback
     * @private
     */
    handleSetActiveSession(tabId, sendResponse) {
        if (this.sessions.has(tabId)) {
            this.setActiveSession(tabId);
            sendResponse({ success: true });

            // Notify popup of change
            this.broadcastToPopup('activeSessionChanged', {
                activeSessionId: this.activeSessionId
            });
        } else {
            sendResponse({ error: COORDINATOR_CONFIG.ERROR_MESSAGES.SESSION_NOT_FOUND });
        }
    }

    /**
     * Handle send message to session request from popup
     * @param {number} tabId - Target tab ID
     * @param {Object} message - Message to send
     * @param {Function} sendResponse - Response callback
     * @private
     */
    async handleSendToSession(tabId, message, sendResponse) {
        try {
            if (!this.sessions.has(tabId)) {
                sendResponse({ error: COORDINATOR_CONFIG.ERROR_MESSAGES.SESSION_NOT_FOUND });
                return;
            }

            const response = await this.sendMessageToTab(tabId, message);
            sendResponse(response);
        } catch (error) {
            console.error(`❌ Error sending message to session ${tabId}:`, error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * Send message to specific tab with timeout and retry
     * @param {number} tabId - Target tab ID
     * @param {Object} message - Message to send
     * @returns {Promise<Object>} Response from tab
     * @private
     */
    async sendMessageToTab(tabId, message) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Message timeout'));
            }, COORDINATOR_CONFIG.MESSAGING.CONTENT_RESPONSE_TIMEOUT);

            chrome.tabs.sendMessage(tabId, message, (response) => {
                clearTimeout(timeout);

                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response || {});
                }
            });
        });
    }

    /**
     * Send message to popup if connected
     * @param {string} action - Action type
     * @param {Object} data - Data to send
     * @private
     */
    sendToPopup(action, data) {
        if (this.popupPort) {
            try {
                this.popupPort.postMessage({ action, data });
            } catch (error) {
                console.warn('❌ Error sending to popup:', error);
                this.popupPort = null;
            }
        }
    }

    /**
     * Broadcast message to popup (alias for sendToPopup)
     * @param {string} action - Action type
     * @param {Object} data - Data to send
     * @private
     */
    broadcastToPopup(action, data) {
        this.sendToPopup(action, data);
    }

    // ============================================================================
    // SETTINGS MANAGEMENT
    // ============================================================================

    /**
     * Load global settings from storage
     * @private
     */
    async loadGlobalSettings() {
        try {
            const result = await chrome.storage.sync.get([
                COORDINATOR_CONFIG.STORAGE.GLOBAL_SETTINGS,
                COORDINATOR_CONFIG.STORAGE.POPUP_SETTINGS,
                COORDINATOR_CONFIG.STORAGE.EXTENSION_ENABLED
            ]);

            this.globalSettings = {
                ...COORDINATOR_CONFIG.DEFAULTS,
                ...result[COORDINATOR_CONFIG.STORAGE.GLOBAL_SETTINGS]
            };

            this.popupSettings = {
                ...COORDINATOR_CONFIG.POPUP_DEFAULTS,
                ...result[COORDINATOR_CONFIG.STORAGE.POPUP_SETTINGS]
            };

            this.extensionEnabled = result[COORDINATOR_CONFIG.STORAGE.EXTENSION_ENABLED] !== false;

            console.log('🎵 Global settings loaded');
        } catch (error) {
            console.error('❌ Error loading global settings:', error);
            this.globalSettings = { ...COORDINATOR_CONFIG.DEFAULTS };
            this.popupSettings = {
                popupFontSize: COORDINATOR_CONFIG.DEFAULTS.popupFontSize,
                openTabBehavior: COORDINATOR_CONFIG.DEFAULTS.openTabBehavior
            };
            this.extensionEnabled = true;
        }
    }

    /**
     * Handle update settings request
     * @param {Object} data - Settings update data
     * @param {Function} sendResponse - Response callback
     * @private
     */
    async handleUpdateSettings(data, sendResponse) {
        try {
            const { settings, scope, tabId } = data;

            if (scope === 'popup') {
                // Update popup-specific settings
                this.popupSettings = { ...this.popupSettings, ...settings };
                await chrome.storage.sync.set({
                    [COORDINATOR_CONFIG.STORAGE.POPUP_SETTINGS]: this.popupSettings
                });
            } else if (scope === 'global') {
                // Update global settings
                this.globalSettings = { ...this.globalSettings, ...settings };
                await chrome.storage.sync.set({
                    [COORDINATOR_CONFIG.STORAGE.GLOBAL_SETTINGS]: this.globalSettings
                });

                // Broadcast to all sessions
                this.broadcastSettingsToAllSessions(settings, 'global');
            } else if (scope === 'song' && tabId) {
                // Forward to specific session for song-specific settings
                await this.sendMessageToTab(tabId, {
                    action: 'updateSettings',
                    data: { settings, scope: 'song' }
                });
            }

            sendResponse({ success: true });
        } catch (error) {
            console.error('❌ Error updating settings:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * Handle get settings request
     * @param {Object} data - Request data
     * @param {Function} sendResponse - Response callback
     * @private
     */
    async handleGetSettings(data, sendResponse) {
        try {
            const { scope, tabId } = data;

            if (scope === 'popup') {
                sendResponse({
                    settings: this.popupSettings,
                    extensionEnabled: this.extensionEnabled
                });
            } else if (scope === 'global') {
                sendResponse({
                    settings: this.globalSettings,
                    extensionEnabled: this.extensionEnabled
                });
            } else if (scope === 'song' && tabId) {
                // Forward to specific session
                const response = await this.sendMessageToTab(tabId, {
                    action: 'getSettings',
                    data: { scope: 'song' }
                });
                sendResponse(response);
            } else {
                sendResponse({
                    settings: this.globalSettings,
                    popupSettings: this.popupSettings,
                    extensionEnabled: this.extensionEnabled
                });
            }
        } catch (error) {
            console.error('❌ Error getting settings:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * Broadcast settings update to all active sessions
     * @param {Object} settings - Settings to broadcast
     * @param {string} scope - Settings scope
     * @private
     */
    async broadcastSettingsToAllSessions(settings, scope) {
        const promises = Array.from(this.sessions.keys()).map(async (tabId) => {
            try {
                await this.sendMessageToTab(tabId, {
                    action: 'updateSettings',
                    data: { settings, scope }
                });
            } catch (error) {
                console.warn(`Could not update settings for session ${tabId}:`, error.message);
            }
        });

        await Promise.allSettled(promises);
    }

    // ============================================================================
    // LYRICS CACHE MANAGEMENT
    // ============================================================================

    /**
     * Set up lyrics cache system
     * @private
     */
    setupLyricsCache() {
        // Set up periodic cache cleanup
        setInterval(() => {
            this.cleanupExpiredLyricsCache();
        }, 60 * 60 * 1000); // Every hour

        console.log('🎵 Lyrics cache system initialized');
    }

    /**
     * Clean up expired lyrics cache entries
     * @private
     */
    cleanupExpiredLyricsCache() {
        const now = Date.now();
        let cleaned = 0;

        for (const [key, value] of this.lyricsCache.entries()) {
            if (now - value.timestamp > COORDINATOR_CONFIG.PERFORMANCE.CACHE_TTL) {
                this.lyricsCache.delete(key);
                cleaned++;
            }
        }

        if (cleaned > 0) {
            console.log(`🎵 Cleaned up ${cleaned} expired lyrics cache entries`);
        }
    }

    // ============================================================================
    // LYRICS FETCHING (Simplified)
    // ============================================================================

    /**
     * Handle lyrics requests with caching and deduplication
     * @param {Object} data - Request data containing song information
     * @param {Function} sendResponse - Response callback
     * @private
     */
    async handleLyricsRequest(data, sendResponse) {
        try {
            const { song } = data;
            const requestId = this.generateRequestId(song);

            // Check for duplicate requests
            if (this.requestQueue.has(requestId)) {
                const existingRequest = this.requestQueue.get(requestId);
                existingRequest.callbacks.push(sendResponse);
                return;
            }

            // Create new request entry
            this.requestQueue.set(requestId, {
                callbacks: [sendResponse],
                timestamp: Date.now()
            });

            // Check cache first
            const cached = this.getCachedLyrics(requestId);
            if (cached) {
                this.respondToAllCallbacks(requestId, cached);
                return;
            }

            // Fetch new lyrics using the original LyricsService logic
            const result = await this.fetchLyrics(song);

            // Cache successful results
            if (result && !result.error) {
                this.setCachedLyrics(requestId, result);
            }

            this.respondToAllCallbacks(requestId, result);

        } catch (error) {
            console.error('❌ Error in lyrics request:', error);
            const errorMessage = this.getErrorMessage(error);
            this.respondToAllCallbacks(data.song ? this.generateRequestId(data.song) : 'unknown', { error: errorMessage });
        }
    }

    /**
     * Respond to all waiting callbacks for a request
     * @param {string} requestId - Request ID
     * @param {Object} result - Result to send
     * @private
     */
    respondToAllCallbacks(requestId, result) {
        const requestEntry = this.requestQueue.get(requestId);
        if (requestEntry) {
            requestEntry.callbacks.forEach(callback => {
                try {
                    callback(result);
                } catch (error) {
                    console.error('❌ Error sending response:', error);
                }
            });
            this.requestQueue.delete(requestId);
        }
    }

    /**
     * Generate unique request identifier for deduplication
     * @param {Object} song - Song information
     * @returns {string} Unique request ID
     * @private
     */
    generateRequestId(song) {
        const normalizedTitle = song.title?.toLowerCase().replace(/[^\w\s]/g, '') || '';
        const normalizedArtist = song.artist?.toLowerCase().replace(/[^\w\s]/g, '') || '';
        return `${normalizedArtist}-${normalizedTitle}`;
    }

    /**
     * Get cached lyrics with TTL checking
     * @param {string} key - Cache key
     * @returns {Object|null} Cached data or null
     * @private
     */
    getCachedLyrics(key) {
        const cached = this.lyricsCache.get(key);
        if (!cached) return null;

        // Check TTL
        if (Date.now() - cached.timestamp > COORDINATOR_CONFIG.PERFORMANCE.CACHE_TTL) {
            this.lyricsCache.delete(key);
            return null;
        }

        console.log('🎵 Using cached lyrics');
        return cached.data;
    }

    /**
     * Store lyrics in cache
     * @param {string} key - Cache key
     * @param {Object} data - Lyrics data
     * @private
     */
    setCachedLyrics(key, data) {
        // Manage cache size
        if (this.lyricsCache.size >= COORDINATOR_CONFIG.PERFORMANCE.MAX_CACHE_SIZE) {
            this.cleanupOldestCacheEntries();
        }

        this.lyricsCache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * Clean up oldest cache entries when cache is full
     * @private
     */
    cleanupOldestCacheEntries() {
        const entries = Array.from(this.lyricsCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        // Remove oldest 25% of entries
        const toRemove = Math.floor(entries.length * 0.25);
        for (let i = 0; i < toRemove; i++) {
            this.lyricsCache.delete(entries[i][0]);
        }
    }

    // ============================================================================
    // ERROR HANDLING & UTILITIES
    // ============================================================================

    /**
     * Enhanced error message generation with context awareness
     * @param {Error|string} error - The error to convert
     * @returns {string} User-friendly error message
     * @private
     */
    getErrorMessage(error) {
        const errorMessage = error?.message || error?.toString() || 'Unknown error';

        // Create error categorization map for better performance
        const errorPatterns = [
            { patterns: ['NetworkError', 'fetch', 'Failed to fetch'], message: COORDINATOR_CONFIG.ERROR_MESSAGES.NETWORK_ERROR },
            { patterns: ['timeout', 'AbortError', 'aborted'], message: COORDINATOR_CONFIG.ERROR_MESSAGES.TIMEOUT },
            { patterns: ['429', 'rate limit', 'Too Many Requests'], message: COORDINATOR_CONFIG.ERROR_MESSAGES.RATE_LIMITED },
            { patterns: ['500', '502', '503', 'server'], message: COORDINATOR_CONFIG.ERROR_MESSAGES.SERVER_ERROR }
        ];

        for (const { patterns, message } of errorPatterns) {
            if (patterns.some(pattern => errorMessage.includes(pattern))) {
                return message;
            }
        }

        return COORDINATOR_CONFIG.ERROR_MESSAGES.GENERIC_ERROR;
    }

    /**
     * Validate song information before processing
     * @param {Object} songInfo - Song information to validate
     * @returns {string|null} Error message if invalid, null if valid
     * @private
     */
    validateSongInfo(songInfo) {
        if (!songInfo || typeof songInfo !== 'object') {
            return COORDINATOR_CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        if (!songInfo.title || typeof songInfo.title !== 'string' || !songInfo.title.trim()) {
            return COORDINATOR_CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        if (!songInfo.artist || typeof songInfo.artist !== 'string' || !songInfo.artist.trim()) {
            return COORDINATOR_CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        return null; // Valid
    }

    /**
     * High-performance lyrics fetching with intelligent retry and fallback
     * @param {Object} songInfo - Song information object
     * @returns {Promise<Object>} Lyrics data or error
     */
    async fetchLyrics(songInfo) {
        console.log('🔍 Fetching lyrics for:', songInfo.title, 'by', songInfo.artist);

        // Comprehensive validation
        const validationError = this.validateSongInfo(songInfo);
        if (validationError) {
            return { error: validationError };
        }

        // Network connectivity check
        if (!navigator.onLine) {
            console.log('❌ No internet connection detected');
            return { error: COORDINATOR_CONFIG.ERROR_MESSAGES.NO_INTERNET };
        }

        try {
            // Try APIs with intelligent priority ordering
            const apiAttempts = [
                { name: 'LRCLib', method: () => this.tryLRCLibWithRetry(songInfo) },
                { name: 'LyricsOVH', method: () => this.tryLyricsOVHWithRetry(songInfo) }
            ];

            for (const { name, method } of apiAttempts) {
                try {
                    console.log(`🎵 Trying ${name} for:`, songInfo.title, 'by', songInfo.artist);
                    const lyrics = await method();

                    if (lyrics) {
                        console.log(`✅ Success with ${name}`);
                        return { lyrics };
                    }
                } catch (apiError) {
                    console.warn(`❌ ${name} failed:`, apiError.message);
                    continue; // Try next API
                }
            }

            console.log('❌ No lyrics found from any source');
            return { error: COORDINATOR_CONFIG.ERROR_MESSAGES.NO_LYRICS_FOUND };

        } catch (error) {
            console.error('❌ Critical error in fetchLyrics:', error);
            return { error: this.getErrorMessage(error) };
        }
    }

    /**
     * LRCLib API with retry mechanism
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics or null
     * @private
     */
    async tryLRCLibWithRetry(songInfo) {
        const maxRetries = COORDINATOR_CONFIG.APIS.LRCLIB.MAX_RETRIES;
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`🔄 LRCLib retry attempt ${attempt}/${maxRetries}`);
                    await this.delay(1000 * attempt); // Exponential backoff
                }

                return await this.tryLRCLib(songInfo);
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
            }
        }

        throw lastError;
    }

    /**
     * Lyrics.ovh API with retry mechanism
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics or null
     * @private
     */
    async tryLyricsOVHWithRetry(songInfo) {
        const maxRetries = COORDINATOR_CONFIG.APIS.LYRICS_OVH.MAX_RETRIES;
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`🔄 LyricsOVH retry attempt ${attempt}/${maxRetries}`);
                    await this.delay(500 * attempt); // Shorter backoff
                }

                return await this.tryLyricsOVH(songInfo);
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
            }
        }

        throw lastError;
    }

    /**
     * Utility delay function for retry mechanisms
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise<void>}
     * @private
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }



    /**
     * Try to fetch lyrics from LRCLib API
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics data or null if failed
     * @private
     */
    async tryLRCLib(songInfo) {
        try {
            console.log('🎵 Trying lrclib.net API for:', songInfo.artist, '-', songInfo.title);

            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim();

            console.log('🧹 Cleaned search:', cleanArtist, '-', cleanTitle);

            // Add timeout to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), COORDINATOR_CONFIG.APIS.LRCLIB.TIMEOUT);

            try {
                // Try search API first to find the best match
                const searchUrl = `${COORDINATOR_CONFIG.APIS.LRCLIB.BASE_URL}/search?track_name=${encodeURIComponent(cleanTitle)}&artist_name=${encodeURIComponent(cleanArtist)}`;

                console.log('🔍 Searching lrclib.net:', searchUrl);

                const searchResponse = await fetch(searchUrl, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': COORDINATOR_CONFIG.APIS.LRCLIB.USER_AGENT
                    }
                });

                if (searchResponse.ok) {
                    const searchResults = await searchResponse.json();
                    console.log('📄 lrclib.net search results:', searchResults.length, 'tracks found');

                    if (searchResults && searchResults.length > 0) {
                        // Find the best match using smart scoring
                        const bestMatch = this.findBestMatch(searchResults, songInfo);
                        console.log('🎯 Best match selected:', bestMatch.artistName, '-', bestMatch.trackName, `(score: ${bestMatch._score})`);

                        clearTimeout(timeoutId);

                        // Check if we have synced lyrics
                        if (bestMatch.syncedLyrics && bestMatch.syncedLyrics.trim()) {
                            console.log('✨ Found synced lyrics from lrclib.net');
                            const parsedLyrics = this.parseLRC(bestMatch.syncedLyrics);
                            parsedLyrics.source = 'lrclib.net';
                            return parsedLyrics;
                        }
                        // Fallback to plain lyrics
                        else if (bestMatch.plainLyrics && bestMatch.plainLyrics.trim()) {
                            console.log('📝 Found plain lyrics from lrclib.net');
                            return {
                                type: 'static',
                                text: this.formatLyrics(bestMatch.plainLyrics),
                                source: 'lrclib.net'
                            };
                        }
                    }
                } else {
                    console.log('❌ lrclib.net search failed:', searchResponse.status, searchResponse.statusText);
                }
            } finally {
                clearTimeout(timeoutId);
            }

            return null;
        } catch (error) {
            console.error('❌ lrclib.net API error:', error.message);

            // Don't throw here, just return null so backup API can be tried
            // The main fetchLyrics method will handle the final error messaging
            if (error.name === 'AbortError') {
                console.log('🕐 lrclib.net request timed out, will try backup API');
            } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                console.log('🌐 lrclib.net network error, will try backup API');
            } else {
                console.log('❓ lrclib.net unknown error, will try backup API');
            }

            return null;
        }
    }

    /**
     * Try to fetch lyrics from Lyrics.ovh API as backup
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics data or null if failed
     * @private
     */
    async tryLyricsOVH(songInfo) {
        try {
            console.log('🎵 Trying lyrics.ovh API for:', songInfo.artist, '-', songInfo.title);

            // Clean up song title (remove extra info in brackets)
            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim(); // Take first artist if multiple

            console.log('🧹 Cleaned search:', cleanArtist, '-', cleanTitle);

            // Add timeout to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), COORDINATOR_CONFIG.APIS.LYRICS_OVH.TIMEOUT);

            const response = await fetch(`${COORDINATOR_CONFIG.APIS.LYRICS_OVH.BASE_URL}/${encodeURIComponent(cleanArtist)}/${encodeURIComponent(cleanTitle)}`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': COORDINATOR_CONFIG.APIS.LYRICS_OVH.USER_AGENT
                }
            });

            clearTimeout(timeoutId);
            console.log('📡 API response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('📄 API response received, lyrics length:', data.lyrics ? data.lyrics.length : 0);

                if (data.lyrics && data.lyrics.trim()) {
                    return {
                        type: 'static',
                        text: this.formatLyrics(data.lyrics),
                        source: 'lyrics.ovh'
                    };
                }
            } else {
                console.log('❌ lyrics.ovh response not ok:', response.status, response.statusText);

                // Handle specific HTTP status codes
                if (response.status === 404) {
                    console.log('📭 lyrics.ovh: Song not found in database');
                } else if (response.status === 429) {
                    console.log('🚫 lyrics.ovh: Rate limited');
                } else if (response.status >= 500) {
                    console.log('🔧 lyrics.ovh: Server error');
                }
            }

            return null;
        } catch (error) {
            console.error('❌ Lyrics.ovh API error:', error.message);

            // Don't throw here, let the main method handle final error messaging
            if (error.name === 'AbortError') {
                console.log('🕐 lyrics.ovh request timed out');
            } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                console.log('🌐 lyrics.ovh network error');
            } else {
                console.log('❓ lyrics.ovh unknown error');
            }

            return null;
        }
    }

    /**
     * Format raw lyrics text by removing timestamps and attribution
     * @param {string} rawLyrics - Raw lyrics text
     * @returns {string} Formatted lyrics
     * @private
     */
    formatLyrics(rawLyrics) {
        if (!rawLyrics) { return ''; }

        return rawLyrics
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0 && !line.match(/^\[.*\]$/)) // Remove timestamp markers
            .filter(line => !COORDINATOR_CONFIG.ATTRIBUTION_FILTERS.some(filter =>
                line.toLowerCase().includes(filter)
            )) // Remove attribution lines
            .join('\n');
    }

    /**
     * Parse LRC file format into structured lyrics data
     * @param {string} lrcContent - LRC format lyrics content
     * @returns {Object} Parsed lyrics with timing information
     * @private
     */
    parseLRC(lrcContent) {
        const lines = lrcContent.split('\n');
        const lyricsLines = [];
        const metadata = {};

        for (const line of lines) {
            // Support multiple time formats: [mm:ss.xx] and [mm:ss.xxx]
            const timeMatch = line.match(/\[(\d{1,2}):(\d{2})\.(\d{2,3})\](.*)$/);
            const metaMatch = line.match(/\[([a-z]+):(.*)\]/);

            if (timeMatch) {
                const minutes = parseInt(timeMatch[1]);
                const seconds = parseInt(timeMatch[2]);
                let centiseconds = parseInt(timeMatch[3]);

                // Handle both 2-digit and 3-digit centiseconds
                if (timeMatch[3].length === 3) {
                    centiseconds = centiseconds / 10; // Convert milliseconds to centiseconds
                }

                const time = minutes * 60 + seconds + centiseconds / 100;
                const text = timeMatch[4].trim();

                if (text) {
                    lyricsLines.push({ time, text });
                }
            } else if (metaMatch) {
                metadata[metaMatch[1]] = metaMatch[2];
            }
        }

        console.log('🎵 Parsed LRC:', lyricsLines.length, 'lines');
        lyricsLines.forEach((line, i) => {
            if (i < 3) { console.log(`📝 Line ${i + 1}: ${line.time}s - "${line.text}"`); }
        });

        return {
            type: 'synced',
            lines: lyricsLines,
            metadata
        };
    }

    /**
     * Find the best match from search results using intelligent scoring
     * @param {Array} searchResults - Array of search results
     * @param {Object} originalSongInfo - Original song information for comparison
     * @returns {Object} Best matching result with score
     * @private
     */
    findBestMatch(searchResults, originalSongInfo) {
        const cleanOriginalTitle = originalSongInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
        const cleanOriginalArtist = originalSongInfo.artist.split(',')[0].trim();

        console.log(`🎯 Analyzing ${searchResults.length} results for: "${cleanOriginalArtist}" - "${cleanOriginalTitle}"`);

        // Use song duration from content script (parsed from .time-info element)
        const currentDuration = originalSongInfo.duration;
        if (currentDuration) {
            const minutes = Math.floor(currentDuration / 60);
            const seconds = currentDuration % 60;
            console.log(`⏱️ Song duration: ${minutes}:${String(seconds).padStart(2, '0')} (${currentDuration}s)`);
        } else {
            console.log(`⏱️ Song duration: unknown`);
        }

        const scoredResults = searchResults.map((result, index) => {
            const scores = this.calculateDetailedSimilarity(result, cleanOriginalTitle, cleanOriginalArtist, currentDuration, index);

            // Calculate weighted total score (out of 100)
            const totalScore =
                scores.titleScore * COORDINATOR_CONFIG.SCORING.TITLE_WEIGHT +
                scores.artistScore * COORDINATOR_CONFIG.SCORING.ARTIST_WEIGHT +
                scores.durationScore * COORDINATOR_CONFIG.SCORING.DURATION_WEIGHT +
                scores.syncedBonus * COORDINATOR_CONFIG.SCORING.SYNCED_WEIGHT +
                scores.keywordBonus * COORDINATOR_CONFIG.SCORING.KEYWORDS_WEIGHT +
                scores.positionBonus * COORDINATOR_CONFIG.SCORING.POSITION_WEIGHT;

            const hasSynced = result.syncedLyrics ? "✅ Synced" : "❌ No sync";
            const duration = result.duration ? `${Math.floor(result.duration / 60)}:${String(result.duration % 60).padStart(2, '0')}` : "Unknown";

            console.log(`📊 [${totalScore.toFixed(1)}] "${result.trackName}" by "${result.artistName}" | ${duration} | ${hasSynced}`);
            console.log(`   └─ Title:${scores.titleScore.toFixed(1)} Artist:${scores.artistScore.toFixed(1)} Duration:${scores.durationScore.toFixed(1)} Synced:${scores.syncedBonus.toFixed(1)} Keywords:${scores.keywordBonus.toFixed(1)}`);

            return {
                ...result,
                _score: totalScore,
                _breakdown: scores
            };
        });

        // Sort by score (highest first)
        scoredResults.sort((a, b) => b._score - a._score);
        console.log(`\n🏆 Top 3 candidates:`);
        scoredResults.slice(0, 3).forEach((result, i) => {
            const syncedIcon = result.syncedLyrics ? '✨' : '📝';
            const duration = result.duration ? `${Math.floor(result.duration / 60)}:${String(result.duration % 60).padStart(2, '0')}` : "?:??";
            console.log(`  ${i + 1}. ${syncedIcon} [${result._score.toFixed(1)}] "${result.trackName}" by "${result.artistName}" (${duration})`);
        });

        const winner = scoredResults[0];
        console.log(`\n🎖️ SELECTED: "${winner.trackName}" by "${winner.artistName}" (Score: ${winner._score.toFixed(1)})`);

        return winner;
    }

    /**
     * Calculate detailed similarity score between search result and original song
     * @param {Object} result - Search result to score
     * @param {string} originalTitle - Original song title
     * @param {string} originalArtist - Original song artist
     * @param {number|null} currentDuration - Current song duration in seconds
     * @param {number} position - Position in search results (0-based)
     * @returns {Object} Detailed similarity scores
     * @private
     */
    calculateDetailedSimilarity(result, originalTitle, originalArtist, currentDuration = null, position = 0) {
        const scores = {
            titleScore: 0,
            artistScore: 0,
            durationScore: 0,
            syncedBonus: 0,
            keywordBonus: 0,
            positionBonus: 0
        };

        // 1. Title similarity (normalized to 0-100)
        scores.titleScore = this.stringSimilarity(
            this.cleanForComparison(originalTitle),
            this.cleanForComparison(result.trackName)
        ) * 100;

        // 2. Artist similarity (normalized to 0-100)
        scores.artistScore = this.stringSimilarity(
            this.cleanForComparison(originalArtist),
            this.cleanForComparison(result.artistName)
        ) * 100;

        // 3. Duration similarity (normalized to 0-100)
        if (currentDuration && result.duration) {
            const durationDiff = Math.abs(currentDuration - result.duration);
            if (durationDiff <= 3) {
                scores.durationScore = 100; // Perfect match within 3 seconds
            } else if (durationDiff <= 10) {
                scores.durationScore = 90;  // Excellent match within 10 seconds
            } else if (durationDiff <= 20) {
                scores.durationScore = 75;  // Good match within 20 seconds
            } else if (durationDiff <= 45) {
                scores.durationScore = 50;  // Acceptable match within 45 seconds
            } else if (durationDiff <= 90) {
                scores.durationScore = 25;  // Poor match within 1.5 minutes
            } else {
                scores.durationScore = Math.max(0, 25 - (durationDiff - 90) / 10); // Linear decay
            }
        } else {
            scores.durationScore = 50; // Neutral score when duration unavailable
        }

        // 4. Synced lyrics bonus (0 or 100)
        scores.syncedBonus = result.syncedLyrics && result.syncedLyrics.trim() ? 100 : 0;

        // 5. Special keywords and format matching (0-100)
        scores.keywordBonus = this.calculateKeywordBonus(originalTitle, result.trackName);

        // 6. Position bonus (small preference for earlier results) (0-100)
        scores.positionBonus = Math.max(0, 100 - position * 5);

        return scores;
    }

    /**
     * Calculate bonus score for special keywords and format matching
     * @param {string} originalTitle - Original song title
     * @param {string} resultTitle - Result song title
     * @returns {number} Keyword bonus score (0-100)
     * @private
     */
    calculateKeywordBonus(originalTitle, resultTitle) {
        let bonus = 0;
        const original = originalTitle.toLowerCase();
        const result = resultTitle.toLowerCase();

        // Special edition keywords
        for (const keyword of COORDINATOR_CONFIG.SPECIAL_KEYWORDS) {
            const originalHas = original.includes(keyword);
            const resultHas = result.includes(keyword);

            if (originalHas && resultHas) {
                bonus += 30; // Strong bonus for matching special keywords
            } else if (originalHas && !resultHas) {
                bonus -= 15; // Penalty for missing expected keywords
            }
        }

        // Featured artist handling
        const featRegex = /\b(feat\.?|ft\.?|featuring)\s+/gi;
        const originalHasFeat = featRegex.test(original);
        const resultHasFeat = featRegex.test(result);

        if (originalHasFeat && resultHasFeat) {
            bonus += 20; // Bonus for both having featured artists
        } else if (originalHasFeat !== resultHasFeat) {
            bonus -= 10; // Small penalty for mismatch
        }

        // Explicit/Clean version handling
        for (const word of COORDINATOR_CONFIG.EXPLICIT_KEYWORDS) {
            if (original.includes(word) && result.includes(word)) {
                bonus += 15;
            }
        }

        return Math.max(0, Math.min(100, bonus)); // Clamp between 0-100
    }    // String similarity calculation using multiple techniques  
    stringSimilarity(str1, str2) {
        if (!str1 || !str2) { return 0; }
        if (str1 === str2) { return 1; }

        // Simple similarity calculation using common words and character overlap
        const words1 = str1.split(/\s+/);
        const words2 = str2.split(/\s+/);

        // Count common words
        const commonWords = words1.filter(word => words2.includes(word)).length;
        const totalWords = Math.max(words1.length, words2.length);
        const wordSimilarity = totalWords > 0 ? (commonWords / totalWords) : 0;

        // Character similarity (Levenshtein-like)
        const maxLength = Math.max(str1.length, str2.length);
        if (maxLength === 0) { return 1; }

        let matches = 0;
        const minLength = Math.min(str1.length, str2.length);
        for (let i = 0; i < minLength; i++) {
            if (str1[i] === str2[i]) { matches++; }
        }
        const charSimilarity = matches / maxLength;

        // Combine word and character similarity
        return (wordSimilarity * 0.7) + (charSimilarity * 0.3);
    }

    /**
     * Clean string for comparison by removing special characters and normalizing spacing
     * @param {string} str - String to clean
     * @returns {string} Cleaned string
     * @private
     */
    cleanForComparison(str) {
        if (!str) { return ''; }
        return str.toLowerCase()
            .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
            .replace(/\s+/g, ' ')     // Normalize multiple spaces
            .trim();
    }

    /**
     * Note: Song duration is now retrieved by the content script from .time-info element
     * and passed with the song info in the fetchLyrics message for better matching accuracy
     */
}

// ============================================================================
// COORDINATOR INITIALIZATION
// ============================================================================

/**
 * Initialize the background coordinator when the service worker starts
 */
const coordinator = new BackgroundCoordinator();

/**
 * Service worker installation event
 */
self.addEventListener('install', (event) => {
    console.log('🎵 Live YT Music Lyrics Background Coordinator installed');
    event.waitUntil(self.skipWaiting());
});

/**
 * Service worker activation event
 */
self.addEventListener('activate', (event) => {
    console.log('🎵 Live YT Music Lyrics Background Coordinator activated');
    event.waitUntil(self.clients.claim());
});

/**
 * Handle extension installation
 */
chrome.runtime.onInstalled.addListener(() => {
    console.log('🎵 YouTube Music Lyrics Extension installed');
});

/**
 * Performance monitoring
 */
if (typeof performance !== 'undefined') {
    setInterval(() => {
        const { metrics } = coordinator;
        if (metrics.sessions > 0 || metrics.messages > 0) {
            console.log('📊 Coordinator Performance:', {
                activeSessions: coordinator.sessions.size,
                totalMessages: metrics.messages,
                lyricsRequests: metrics.lyricsRequests,
                errors: metrics.errors
            });
        }
    }, 5 * 60 * 1000);
}

# Live YT Music Lyrics Extension - Validation Checklist

## Architecture Validation (Phase 6)

This checklist validates the new coordinator-based architecture implemented across all phases.

### ✅ Phase 1: Background Script Coordinator

**Background Coordinator Functionality:**
- [ ] Background script initializes without errors
- [ ] Session registry is created and maintained
- [ ] Message routing works between components
- [ ] Port connections are established for real-time communication
- [ ] Session cleanup works when tabs are closed
- [ ] Lyrics caching system is functional
- [ ] Global settings are loaded and managed

**Session Management:**
- [ ] Sessions are registered when content scripts start
- [ ] Session heartbeats are received and processed
- [ ] Session data is updated in real-time
- [ ] Inactive sessions are cleaned up automatically
- [ ] Active session switching works correctly

### ✅ Phase 2: Content Script Communication

**Content Script as Single Source of Truth:**
- [ ] Content script generates unique session ID
- [ ] Heartbeat system sends complete session state every 2 seconds
- [ ] Session updates are sent for song changes, lyrics changes, theme changes
- [ ] Content script handles coordinator messages (settings, playback control)
- [ ] Backward compatibility with popup is maintained
- [ ] Cleanup properly stops heartbeat system

**Real-time Updates:**
- [ ] Song changes trigger session updates
- [ ] Lyrics data changes trigger session updates
- [ ] Theme color changes trigger session updates
- [ ] Settings changes trigger session updates
- [ ] Playback state changes are included in heartbeats

### ✅ Phase 3: Popup as Pure Controller

**Popup Controller Architecture:**
- [ ] <PERSON><PERSON> connects to coordinator via port
- [ ] <PERSON><PERSON> receives session data from coordinator
- [ ] Popup displays data without managing sessions directly
- [ ] Popup sends commands to coordinator instead of content scripts
- [ ] Connection loss detection and recovery works
- [ ] Error handling and reconnection is functional

**UI Display:**
- [ ] Song information displays correctly from session data
- [ ] Playback state displays correctly from session data
- [ ] Lyrics display correctly from session data
- [ ] Theme colors apply correctly from session data
- [ ] Session switching works (if multiple tabs)

### ✅ Phase 4: Unified Settings System

**Settings Format Consistency:**
- [ ] Content script uses: `SYNC_OFFSET`, `AUTO_SCROLL`, `FONT_SIZE`, `HIGHLIGHT_COLOR`, `DYNAMIC_THEME_COLORS`
- [ ] Background coordinator uses identical format
- [ ] Popup-specific settings are separate: `popupFontSize`, `openTabBehavior`
- [ ] Default values are identical across all components

**Settings Synchronization:**
- [ ] Global settings sync across all sessions
- [ ] Song-specific settings work correctly
- [ ] Popup-specific settings are managed separately
- [ ] Settings updates propagate in real-time
- [ ] Settings persistence works correctly

### ✅ Phase 5: Theme Color Coordination

**Content Script Authority:**
- [ ] Content script extracts theme colors from album artwork
- [ ] Content script applies theme colors to its own UI
- [ ] Content script sends theme color updates to coordinator
- [ ] Content script includes theme color in heartbeat data

**Coordinator Relay:**
- [ ] Background coordinator receives theme color updates
- [ ] Background coordinator relays updates to popup
- [ ] Background coordinator maintains theme color in session data

**Popup Display:**
- [ ] Popup receives theme color updates via session updates
- [ ] Popup applies theme colors to its UI
- [ ] Theme color consistency is maintained

### ✅ Phase 6: Testing and Validation

**Multi-tab Scenarios:**
- [ ] Multiple YouTube Music tabs are tracked separately
- [ ] Each tab maintains its own session state
- [ ] Session switching works correctly in popup
- [ ] Settings sync works across multiple tabs
- [ ] Theme colors are independent per tab

**Lyrics Functionality:**
- [ ] Lyrics fetching works through coordinator
- [ ] Synced lyrics display correctly with timing
- [ ] Static lyrics display correctly
- [ ] Lyrics synchronization works with playback
- [ ] Lyrics caching works correctly

**Settings Management:**
- [ ] Global settings apply to all sessions
- [ ] Song-specific settings work correctly
- [ ] Settings modal works in content script
- [ ] Settings sync between popup and content script
- [ ] Settings persistence across browser restarts

**Theme Color System:**
- [ ] Dynamic theme colors extract from album artwork
- [ ] Manual theme colors work correctly
- [ ] Theme colors sync between content script and popup
- [ ] Theme color transitions are smooth
- [ ] Theme colors persist correctly

**Error Handling:**
- [ ] Connection failures are handled gracefully
- [ ] Missing components don't break functionality
- [ ] Invalid data is handled correctly
- [ ] Recovery mechanisms work properly
- [ ] User-friendly error messages are shown

**Performance:**
- [ ] Heartbeat system doesn't cause performance issues
- [ ] Message volume is reasonable
- [ ] Memory usage is acceptable
- [ ] CPU usage is minimal
- [ ] No memory leaks detected

## Manual Testing Scenarios

### Scenario 1: Single Tab Usage
1. Open YouTube Music in one tab
2. Play a song
3. Open extension popup
4. Verify song info, playback state, and lyrics display
5. Test settings changes
6. Test theme color extraction

### Scenario 2: Multi-tab Usage
1. Open YouTube Music in multiple tabs
2. Play different songs in each tab
3. Open extension popup
4. Test session switching
5. Verify independent settings and theme colors
6. Test closing tabs and session cleanup

### Scenario 3: Settings Synchronization
1. Change global settings in popup
2. Verify changes apply to all tabs
3. Change song-specific settings in content script
4. Verify settings are isolated per song
5. Test settings persistence across browser restart

### Scenario 4: Theme Color Coordination
1. Enable dynamic theme colors
2. Play songs with different album artwork
3. Verify theme colors change automatically
4. Test manual theme color override
5. Verify theme colors sync between content script and popup

### Scenario 5: Error Recovery
1. Disable/enable extension
2. Refresh YouTube Music tabs
3. Close and reopen popup
4. Test with network issues
5. Verify graceful degradation

## Automated Testing

Run the test script: `test-architecture.js`

**Expected Results:**
- All component validation tests pass
- Background coordinator tests pass
- Content script communication tests pass
- Popup controller tests pass
- Settings synchronization tests pass
- Theme color coordination tests pass
- Multi-tab session management tests pass

## Success Criteria

**Architecture is considered successful if:**
- ✅ All manual testing scenarios work correctly
- ✅ All automated tests pass
- ✅ No console errors during normal operation
- ✅ Performance is acceptable (no lag or high CPU usage)
- ✅ Memory usage is stable (no leaks)
- ✅ User experience is smooth and responsive
- ✅ Multi-tab scenarios work correctly
- ✅ Settings sync works reliably
- ✅ Theme coordination works consistently
- ✅ Error recovery is graceful

## Known Limitations

**Current limitations to be aware of:**
- Popup must be reopened to see session updates (by design)
- Theme color extraction requires album artwork to be loaded
- Settings changes require active YouTube Music tab
- Lyrics depend on external API availability
- Dynamic theme colors may not work with all album artwork

## Future Improvements

**Potential enhancements for future versions:**
- Real-time popup updates via port messaging
- Improved theme color extraction algorithms
- Better error recovery mechanisms
- Enhanced multi-tab coordination
- Performance optimizations
- Additional settings options

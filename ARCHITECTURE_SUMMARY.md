# Live YT Music Lyrics Extension - New Architecture Summary

## Overview

The Live YT Music Lyrics Extension has been completely rebuilt with a new coordinator-based architecture that provides better multi-tab support, improved reliability, and cleaner separation of concerns.

## Architecture Components

### 1. Background Script Coordinator (`background.js`)
**Role:** Central coordinator and message router

**Responsibilities:**
- Manages session registry for all active YouTube Music tabs
- Routes messages between content scripts and popup
- Handles lyrics caching and API requests
- Manages global settings and extension state
- Provides real-time communication via ports
- Cleans up inactive sessions

**Key Features:**
- Session heartbeat monitoring
- Automatic session cleanup
- Centralized lyrics caching
- Global settings management
- Port-based real-time communication

### 2. Content Script (`content.js`)
**Role:** Single source of truth for each YouTube Music tab

**Responsibilities:**
- Manages complete session state (song, playback, lyrics, settings, theme)
- Sends heartbeat messages with full state every 2 seconds
- Handles lyrics display and synchronization
- Manages theme color extraction and application
- Processes settings changes and playback commands
- Maintains backward compatibility

**Key Features:**
- Unique session ID generation
- Comprehensive heartbeat system
- Real-time session updates
- Theme color authority
- Settings management (global + song-specific)
- Coordinator message handling

### 3. Popup Controller (`popup.js`)
**Role:** Pure display and control interface

**Responsibilities:**
- Connects to background coordinator via port
- Displays session data received from coordinator
- Sends commands to coordinator (not directly to content scripts)
- Manages popup-specific settings only
- Handles connection loss and recovery

**Key Features:**
- Port-based coordinator connection
- Session data display
- Command relay to coordinator
- Popup-specific settings
- Error recovery and reconnection

## Data Flow

### Session Management Flow
```
Content Script → Background Coordinator → Popup
     ↓                    ↓                ↓
1. Heartbeat          2. Session         3. UI Update
   (every 2s)            Registry           Display
```

### Settings Synchronization Flow
```
Popup → Background Coordinator → Content Script
  ↓            ↓                      ↓
1. Settings  2. Route to           3. Apply &
   Update       Session               Save
```

### Theme Color Coordination Flow
```
Content Script → Background Coordinator → Popup
     ↓                    ↓                ↓
1. Extract Color     2. Relay Update   3. Apply Theme
   from Album           to Popup          to UI
```

## Key Improvements

### 1. Multi-tab Support
- **Before:** Single tab focus with complex tab switching
- **After:** Independent session management for each tab
- **Benefit:** Seamless multi-tab experience with isolated state

### 2. Reliability
- **Before:** Direct popup ↔ content script communication
- **After:** Coordinator-mediated communication with heartbeats
- **Benefit:** Better error recovery and connection management

### 3. Settings Management
- **Before:** Inconsistent settings format across components
- **After:** Unified settings format with proper scoping
- **Benefit:** Reliable settings sync and proper isolation

### 4. Theme Color System
- **Before:** Popup-managed theme extraction
- **After:** Content script authority with coordinator relay
- **Benefit:** Consistent theme colors and better performance

### 5. Code Organization
- **Before:** Tightly coupled components with mixed responsibilities
- **After:** Clear separation of concerns with defined interfaces
- **Benefit:** Easier maintenance and future enhancements

## Message Types

### Content Script → Background Coordinator
- `sessionHeartbeat`: Complete session state (every 2s)
- `sessionUpdate`: Real-time updates (song/lyrics/theme changes)
- `fetchLyrics`: Lyrics request (routed to API)

### Background Coordinator → Content Script
- `updateSettings`: Settings update from popup
- `getSettings`: Settings request from popup
- `playPause`: Playback control from popup
- `seekToTime`: Seek request from popup
- `getSessionState`: Full state request

### Popup ↔ Background Coordinator
- `getActiveSessions`: Get all active sessions
- `setActiveSession`: Switch active session
- `sendToSession`: Send message to specific session
- `updateSettings`: Update settings
- `getSettings`: Get settings

### Background Coordinator → Popup (via port)
- `initialSessions`: Initial session data on connection
- `sessionUpdate`: Real-time session updates
- `newSession`: New session detected
- `sessionClosed`: Session closed
- `activeSessionChanged`: Active session changed

## Settings Architecture

### Content Script Settings (Synced)
```javascript
{
    SYNC_OFFSET: 0,           // seconds to offset lyrics sync
    AUTO_SCROLL: true,        // enable auto-scroll
    FONT_SIZE: 20,           // font size in px
    HIGHLIGHT_COLOR: '#ff0000', // active line highlight color
    DYNAMIC_THEME_COLORS: false // enable dynamic theme colors
}
```

### Popup-Specific Settings (Local)
```javascript
{
    popupFontSize: 13,        // popup font size
    openTabBehavior: 'tab'    // 'tab' or 'popup'
}
```

### Settings Scopes
- **Global:** Applied to all songs across all sessions
- **Song-specific:** Applied only to specific songs
- **Popup:** Applied only to popup interface

## Session State Structure

```javascript
{
    sessionId: "unique-session-id",
    tabId: 123,
    song: {
        title: "Song Title",
        artist: "Artist Name",
        albumCover: "https://...",
        duration: 180
    },
    playbackState: {
        isPlaying: true,
        currentTime: 45.2,
        duration: 180,
        timestamp: 1234567890
    },
    lyricsData: {
        type: "synced", // or "static"
        lines: [...],   // for synced lyrics
        text: "...",    // for static lyrics
        source: "API"
    },
    settings: { /* current settings */ },
    themeColor: "#ff6b35",
    isLyricsTabActive: true,
    lastHeartbeat: 1234567890
}
```

## Error Handling

### Connection Management
- Automatic reconnection on connection loss
- Graceful degradation when components are unavailable
- Timeout handling for all message operations
- Retry mechanisms with exponential backoff

### Session Recovery
- Heartbeat timeout detection
- Automatic session cleanup
- Session state recovery on reconnection
- Fallback to default state when needed

### User Experience
- Non-blocking error handling
- User-friendly error messages
- Graceful feature degradation
- Recovery suggestions when appropriate

## Performance Considerations

### Message Optimization
- Heartbeat deduplication (only send when state changes)
- Efficient session state comparison
- Minimal message payload size
- Batched updates when possible

### Memory Management
- Automatic session cleanup
- Lyrics cache size limits
- Proper event listener cleanup
- Memory leak prevention

### CPU Usage
- Efficient heartbeat scheduling
- Optimized theme color extraction
- Minimal DOM manipulation
- Debounced user interactions

## Future Enhancements

### Planned Improvements
1. **Real-time Popup Updates:** Port-based live updates to popup
2. **Enhanced Theme Colors:** Better color extraction algorithms
3. **Performance Monitoring:** Built-in performance metrics
4. **Advanced Settings:** More customization options
5. **Better Error Recovery:** Improved fallback mechanisms

### Extension Points
1. **Plugin System:** Support for custom lyrics sources
2. **Theme System:** Custom theme support
3. **Export/Import:** Settings and data portability
4. **Analytics:** Usage statistics and optimization
5. **Accessibility:** Enhanced accessibility features

## Migration Notes

### Breaking Changes
- Popup no longer directly communicates with content scripts
- Settings format has been standardized
- Theme color management has moved to content script
- Session management is now centralized

### Backward Compatibility
- Old settings are automatically migrated
- Legacy message handlers are maintained
- Graceful fallback for missing components
- Smooth transition for existing users

## Conclusion

The new coordinator-based architecture provides a solid foundation for the Live YT Music Lyrics Extension with improved reliability, better multi-tab support, and cleaner code organization. The separation of concerns makes the extension more maintainable and extensible for future enhancements.

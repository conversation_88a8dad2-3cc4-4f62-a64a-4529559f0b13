/**
 * Live YT Music Lyrics - Popup Controller
 *
 * New architecture: Pure controller/display interface that communicates
 * with background coordinator. Content scripts are the single source of truth.
 *
 * <AUTHOR> Acharya
 * @version 3.0.0
 * @license MIT
 */

'use strict';

// ============================================================================
// CONFIGURATION
// ============================================================================

const POPUP_CONFIG = {
    // Theme and styling
    THEME: {
        DEFAULT_COLOR: '#ff6b35',
        TRANSITION_DURATION: 400,
        ANIMATION_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },

    // Timing for UI updates
    TIMING: {
        LYRICS_SYNC_INTERVAL: 100,   // Smooth lyrics highlighting
        DEBOUNCE_DELAY: 300,
        RETRY_DELAY: 2000,
        CONNECTION_TIMEOUT: 5000,
        HEARTBEAT_TIMEOUT: 10000
    },

    // Settings storage keys (popup-specific only)
    STORAGE: {
        POPUP_SETTINGS: 'ytmusic_popup_settings'
    },

    // Default settings (popup-specific only)
    DEFAULTS: {
        popupFontSize: 13,      // Font size for popup only
        openTabBehavior: 'tab'  // 'tab' or 'popup'
    },

    // Error messages
    ERRORS: {
        NO_SESSIONS: 'No active YouTube Music sessions found. Please open YouTube Music and play a song.',
        CONNECTION_FAILED: 'Failed to connect to background coordinator. Please refresh the page.',
        SESSION_LOST: 'Connection to music session lost. Please refresh the page.',
        COORDINATOR_ERROR: 'Background coordinator error. Please try again.'
    }
};

// ============================================================================
// NEW POPUP CONTROLLER CLASS
// ============================================================================

class PopupController {
    constructor() {
        // Core state from coordinator
        this.sessions = [];
        this.activeSessionId = null;
        this.currentSession = null;

        // UI state
        this.isMinimized = false;
        this.isSettingsOpen = false;
        this.activeLineIndex = -1;

        // Popup-specific settings
        this.popupSettings = { ...POPUP_CONFIG.DEFAULTS };
        this.currentThemeColor = POPUP_CONFIG.THEME.DEFAULT_COLOR;

        // UI elements cache
        this.elements = {};

        // Communication with coordinator
        this.coordinatorPort = null;
        this.connectionState = 'disconnected'; // 'disconnected', 'connecting', 'connected'
        this.lastHeartbeat = null;

        // UI update intervals
        this.lyricsInterval = null;
        this.heartbeatCheckInterval = null;

        // Error recovery
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.initializePopup();
    }

    /**
     * Initialize the popup with coordinator connection
     */
    async initializePopup() {
        const loadingOverlay = document.getElementById('initial-loading');

        try {
            console.log('🎵 Initializing Popup Controller');

            // Show loading state
            if (loadingOverlay) {
                loadingOverlay.classList.remove('hidden');
            }

            // Initialize components
            this.cacheElements();
            await this.loadPopupSettings();
            this.setupEventListeners();
            await this.connectToCoordinator();
            this.startLyricsSync();
            this.startHeartbeatMonitoring();

            // Hide loading state
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.classList.add('hidden');
                    loadingOverlay.style.opacity = '';
                }, 300);
            }

            console.log('✅ Popup initialization completed');
        } catch (error) {
            console.error('❌ Popup initialization failed:', error);

            // Hide loading and show error
            if (loadingOverlay) {
                loadingOverlay.classList.add('hidden');
            }

            this.showErrorState('Failed to initialize popup. Please try again.');
        }
    }

    // ============================================================================
    // COORDINATOR COMMUNICATION
    // ============================================================================

    /**
     * Connect to background coordinator via port
     */
    async connectToCoordinator() {
        try {
            console.log('🎵 Connecting to background coordinator...');
            this.connectionState = 'connecting';

            // Create port connection
            this.coordinatorPort = chrome.runtime.connect({ name: 'popup' });

            // Set up message handlers
            this.coordinatorPort.onMessage.addListener((message) => {
                this.handleCoordinatorMessage(message);
            });

            this.coordinatorPort.onDisconnect.addListener(() => {
                console.warn('🎵 Coordinator connection lost');
                this.connectionState = 'disconnected';
                this.handleConnectionLoss();
            });

            this.connectionState = 'connected';
            console.log('✅ Connected to coordinator');

        } catch (error) {
            console.error('❌ Failed to connect to coordinator:', error);
            this.connectionState = 'disconnected';
            throw error;
        }
    }

    /**
     * Handle messages from coordinator
     */
    handleCoordinatorMessage(message) {
        const { action, data } = message;

        switch (action) {
            case 'initialSessions':
                this.handleInitialSessions(data);
                break;

            case 'sessionUpdate':
                this.handleSessionUpdate(data);
                break;

            case 'newSession':
                this.handleNewSession(data);
                break;

            case 'sessionClosed':
                this.handleSessionClosed(data);
                break;

            case 'activeSessionChanged':
                this.handleActiveSessionChanged(data);
                break;

            case 'sessionsUpdated':
                this.handleSessionsUpdated(data);
                break;

            default:
                console.warn('🔶 Unknown coordinator message:', action);
        }
    }

    /**
     * Send message to coordinator
     */
    async sendToCoordinator(action, data = {}) {
        return new Promise((resolve, reject) => {
            if (!this.coordinatorPort || this.connectionState !== 'connected') {
                reject(new Error('Not connected to coordinator'));
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('Coordinator message timeout'));
            }, POPUP_CONFIG.TIMING.CONNECTION_TIMEOUT);

            chrome.runtime.sendMessage({
                action,
                data
            }, (response) => {
                clearTimeout(timeout);

                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Handle connection loss and attempt reconnection
     */
    async handleConnectionLoss() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.showErrorState('Connection lost. Please refresh the popup.');
            return;
        }

        this.reconnectAttempts++;
        console.log(`🎵 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

        setTimeout(async () => {
            try {
                await this.connectToCoordinator();
                this.reconnectAttempts = 0;
            } catch (error) {
                console.error('❌ Reconnection failed:', error);
                this.handleConnectionLoss();
            }
        }, POPUP_CONFIG.TIMING.RETRY_DELAY);
    }

    // ============================================================================
    // SESSION MANAGEMENT
    // ============================================================================

    /**
     * Handle initial sessions data from coordinator
     */
    handleInitialSessions(data) {
        const { sessions, activeSessionId } = data;
        console.log('🎵 Received initial sessions:', sessions.length);

        this.sessions = sessions;
        this.activeSessionId = activeSessionId;
        this.updateCurrentSession();
        this.updateUI();
    }

    /**
     * Handle session update from coordinator
     */
    handleSessionUpdate(data) {
        const { tabId } = data;

        // Find and update the session
        const sessionIndex = this.sessions.findIndex(s => s.tabId === tabId);
        if (sessionIndex !== -1) {
            this.sessions[sessionIndex] = { ...this.sessions[sessionIndex], ...data };

            // Update current session if this is the active one
            if (tabId === this.activeSessionId) {
                this.updateCurrentSession();
                this.updateUI();
            }
        }
    }

    /**
     * Handle new session from coordinator
     */
    handleNewSession(data) {
        console.log('🎵 New session detected:', data.tabId);
        this.sessions.push(data);
        this.updateUI();
    }

    /**
     * Handle session closed from coordinator
     */
    handleSessionClosed(data) {
        const { tabId } = data;
        console.log('🎵 Session closed:', tabId);

        this.sessions = this.sessions.filter(s => s.tabId !== tabId);

        if (tabId === this.activeSessionId) {
            this.activeSessionId = null;
            this.currentSession = null;
        }

        this.updateUI();
    }

    /**
     * Handle active session changed from coordinator
     */
    handleActiveSessionChanged(data) {
        const { activeSessionId } = data;
        console.log('🎵 Active session changed to:', activeSessionId);

        this.activeSessionId = activeSessionId;
        this.updateCurrentSession();
        this.updateUI();
    }

    /**
     * Handle sessions updated from coordinator
     */
    handleSessionsUpdated(data) {
        const { sessions, activeSessionId } = data;
        console.log('🎵 Sessions updated:', sessions.length);

        this.sessions = sessions;
        this.activeSessionId = activeSessionId;
        this.updateCurrentSession();
        this.updateUI();
    }

    /**
     * Update current session reference
     */
    updateCurrentSession() {
        this.currentSession = this.sessions.find(s => s.tabId === this.activeSessionId) || null;
    }

    /**
     * Switch to different session
     */
    async switchToSession(tabId) {
        try {
            await this.sendToCoordinator('setActiveSession', { tabId });
        } catch (error) {
            console.error('❌ Failed to switch session:', error);
        }
    }

    // ============================================================================
    // SETTINGS MANAGEMENT
    // ============================================================================

    /**
     * Load popup-specific settings
     */
    async loadPopupSettings() {
        try {
            const result = await chrome.storage.sync.get([POPUP_CONFIG.STORAGE.POPUP_SETTINGS]);
            this.popupSettings = {
                ...POPUP_CONFIG.DEFAULTS,
                ...result[POPUP_CONFIG.STORAGE.POPUP_SETTINGS]
            };
            console.log('🎵 Popup settings loaded:', this.popupSettings);
        } catch (error) {
            console.error('❌ Error loading popup settings:', error);
            this.popupSettings = { ...POPUP_CONFIG.DEFAULTS };
        }
    }

    /**
     * Save popup-specific settings
     */
    async savePopupSettings() {
        try {
            await chrome.storage.sync.set({
                [POPUP_CONFIG.STORAGE.POPUP_SETTINGS]: this.popupSettings
            });
            console.log('🎵 Popup settings saved');
        } catch (error) {
            console.error('❌ Error saving popup settings:', error);
        }
    }

    /**
     * Update session settings via coordinator
     */
    async updateSessionSettings(settings, scope = 'global') {
        try {
            if (!this.activeSessionId) {
                throw new Error('No active session');
            }

            await this.sendToCoordinator('updateSettings', {
                settings,
                scope,
                tabId: this.activeSessionId
            });
        } catch (error) {
            console.error('❌ Failed to update session settings:', error);
        }
    }

    // ============================================================================
    // UI MANAGEMENT
    // ============================================================================

    /**
     * Update the entire UI based on current session
     */
    updateUI() {
        if (!this.currentSession) {
            this.showNoSessionState();
            return;
        }

        this.updateSongInfo();
        this.updatePlaybackState();
        this.updateLyrics();
        this.updateTheme();
        this.updateSessionSwitcher();
    }

    /**
     * Update song information display
     */
    updateSongInfo() {
        if (!this.currentSession?.song) return;

        const { song } = this.currentSession;

        if (this.elements.songTitle) {
            this.elements.songTitle.textContent = song.title || 'Unknown Title';
        }

        if (this.elements.songArtist) {
            this.elements.songArtist.textContent = song.artist || 'Unknown Artist';
        }

        // Update album cover if available
        if (this.elements.albumCover && song.albumCover) {
            this.elements.albumCover.src = song.albumCover;
            this.elements.albumCover.style.display = 'block';
            if (this.elements.albumCoverPlaceholder) {
                this.elements.albumCoverPlaceholder.style.display = 'none';
            }
        } else if (this.elements.albumCoverPlaceholder) {
            this.elements.albumCoverPlaceholder.style.display = 'block';
            if (this.elements.albumCover) {
                this.elements.albumCover.style.display = 'none';
            }
        }
    }

    /**
     * Update playback state display
     */
    updatePlaybackState() {
        if (!this.currentSession?.playbackState) return;

        const { playbackState } = this.currentSession;
        const { isPlaying, currentTime, duration } = playbackState;

        // Update play/pause button
        if (this.elements.playPauseButton) {
            this.elements.playPauseButton.innerHTML = isPlaying ? '⏸️' : '▶️';
            this.elements.playPauseButton.title = isPlaying ? 'Pause' : 'Play';
        }

        // Update progress bar
        if (duration > 0) {
            const progress = (currentTime / duration) * 100;

            if (this.elements.progressFill) {
                this.elements.progressFill.style.width = `${progress}%`;
            }

            if (this.elements.miniProgressFill) {
                this.elements.miniProgressFill.style.width = `${progress}%`;
            }
        }

        // Update time displays
        if (this.elements.currentTime) {
            this.elements.currentTime.textContent = this.formatTime(currentTime);
        }

        if (this.elements.totalTime) {
            this.elements.totalTime.textContent = this.formatTime(duration);
        }
    }

    /**
     * Update lyrics display
     */
    updateLyrics() {
        if (!this.currentSession?.lyricsData) {
            this.showNoLyricsState();
            return;
        }

        const { lyricsData } = this.currentSession;

        if (lyricsData.type === 'synced') {
            this.displaySyncedLyrics(lyricsData);
        } else if (lyricsData.type === 'static') {
            this.displayStaticLyrics(lyricsData);
        }
    }

    /**
     * Update theme based on current session
     */
    updateTheme() {
        const themeColor = this.currentSession?.themeColor || POPUP_CONFIG.THEME.DEFAULT_COLOR;

        if (themeColor !== this.currentThemeColor) {
            this.currentThemeColor = themeColor;
            this.applyThemeColor(themeColor);
        }
    }

    /**
     * Show no session state
     */
    showNoSessionState() {
        if (this.elements.popupMain) {
            this.elements.popupMain.innerHTML = `
                <div class="no-session-state">
                    <div class="no-session-icon">🎵</div>
                    <div class="no-session-title">No Active Sessions</div>
                    <div class="no-session-message">${POPUP_CONFIG.ERRORS.NO_SESSIONS}</div>
                </div>
            `;
        }
    }

    /**
     * Show error state
     */
    showErrorState(message) {
        if (this.elements.popupMain) {
            this.elements.popupMain.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">❌</div>
                    <div class="error-title">Connection Error</div>
                    <div class="error-message">${message}</div>
                    <button class="error-retry" onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    }

    /**
     * Cache DOM elements for performance
     */
    cacheElements() {
        // Header elements
        this.elements.settingsButton = document.getElementById('settings-button');
        this.elements.openTabButton = document.getElementById('open-tab-button');
        this.elements.masterToggle = document.getElementById('master-toggle');

        // Main popup elements
        this.elements.popupMain = document.getElementById('popup-main');
        this.elements.nowPlaying = document.getElementById('now-playing');
        this.elements.minimizedPlayer = document.getElementById('minimized-player');
        this.elements.lyricsContainer = document.getElementById('lyrics-container');

        // Tab navigation
        this.elements.prevTab = document.getElementById('prev-tab');
        this.elements.nextTab = document.getElementById('next-tab');

        // Song info elements
        this.elements.albumCover = document.getElementById('album-cover');
        this.elements.albumCoverContainer = document.getElementById('album-cover-container');
        this.elements.albumCoverPlaceholder = document.getElementById('album-cover-placeholder');
        this.elements.songTitle = document.getElementById('song-title');
        this.elements.songArtist = document.getElementById('song-artist');

        // Progress elements
        this.elements.progressContainer = document.querySelector('.progress-container');
        this.elements.progressBar = document.querySelector('.progress-bar');
        this.elements.progressFill = document.getElementById('progress-fill');
        this.elements.currentTime = document.getElementById('current-time');
        this.elements.totalTime = document.getElementById('total-time');
        this.elements.miniProgressBar = document.querySelector('.mini-progress-bar');
        this.elements.miniProgressFill = document.getElementById('mini-progress-fill');

        // Control buttons
        this.elements.prevButton = document.getElementById('prev-button');
        this.elements.playPauseButton = document.getElementById('play-pause-button');
        this.elements.nextButton = document.getElementById('next-button');
        this.elements.playIcon = document.getElementById('play-icon');
        this.elements.pauseIcon = document.getElementById('pause-icon');

        // Minimize/expand buttons
        this.elements.minimizeButton = document.getElementById('minimize-button');
        this.elements.expandButton = document.getElementById('expand-button');

        // Mini player elements
        this.elements.miniPlayerLayout = document.querySelector('.minimized-player-layout'); // Updated class
        this.elements.miniAlbumCover = document.getElementById('mini-album-cover');
        this.elements.miniCoverPlaceholder = document.getElementById('mini-cover-placeholder');
        this.elements.miniSongTitle = document.getElementById('mini-song-title');
        this.elements.miniSongArtist = document.getElementById('mini-song-artist');

        // Mini control buttons
        this.elements.miniPrevButton = document.getElementById('mini-prev-button');
        this.elements.miniPlayPauseButton = document.getElementById('mini-play-pause-button');
        this.elements.miniNextButton = document.getElementById('mini-next-button');
        this.elements.miniPlayIcon = document.getElementById('mini-play-icon');
        this.elements.miniPauseIcon = document.getElementById('mini-pause-icon');

        // Lyrics elements - EXACT copy from content.js structure
        this.elements.ytmusicLyricsWrapper = document.getElementById('ytmusic-lyrics-wrapper');
        this.elements.ytmusicLyricsContent = document.getElementById('ytmusic-lyrics-content');
        this.elements.ytmusicLyricsLoading = document.getElementById('ytmusic-lyrics-loading');
        this.elements.ytmusicLyricsError = document.getElementById('ytmusic-lyrics-error');
        this.elements.ytmusicLyricsSource = document.getElementById('ytmusic-lyrics-source');
        this.elements.lyricsLinesContainer = document.getElementById('lyrics-lines-container');

        // Legacy support
        this.elements.lyricsContent = this.elements.ytmusicLyricsContent;
        this.elements.lyricsLoading = this.elements.ytmusicLyricsLoading;
        this.elements.lyricsError = this.elements.ytmusicLyricsError;
        this.elements.lyricsLines = this.elements.lyricsLinesContainer;

        // Settings page elements
        this.elements.settingsPage = document.getElementById('settings-page');
        this.elements.closeSettings = document.getElementById('close-settings');
        this.elements.activeSongSelector = document.getElementById('active-song-selector');
        this.elements.currentSongInfo = document.getElementById('current-song-info');
        this.elements.openTabBehavior = document.getElementById('open-tab-behavior');
        this.elements.dynamicColorsToggle = document.getElementById('dynamic-colors-toggle');
        this.elements.autoScrollToggle = document.getElementById('auto-scroll-toggle');
        this.elements.popupFontSizeSlider = document.getElementById('popup-font-size-slider');
        this.elements.popupFontSizeValue = document.getElementById('popup-font-size-value');
        this.elements.syncOffsetSlider = document.getElementById('sync-offset-slider');
        this.elements.syncOffsetValue = document.getElementById('sync-offset-value');
        this.elements.highlightColorPicker = document.getElementById('highlight-color-picker');
        this.elements.highlightColorValue = document.getElementById('highlight-color-value');
        this.elements.exportSettings = document.getElementById('export-settings');
        this.elements.importSettings = document.getElementById('import-settings');
        this.elements.clearCache = document.getElementById('clear-cache');
        this.elements.resetSettings = document.getElementById('reset-settings');
        this.elements.githubLink = document.getElementById('github-link');
        this.elements.reportIssue = document.getElementById('report-issue');
    }

    // ============================================================================
    // LYRICS DISPLAY
    // ============================================================================

    /**
     * Display synced lyrics
     */
    displaySyncedLyrics(lyricsData) {
        if (!this.elements.lyricsContainer || !lyricsData.lines) return;

        const lyricsHTML = lyricsData.lines.map((line, index) =>
            `<div class="ytmusic-lyrics-line" data-time="${line.time}" data-index="${index}">
                ${this.escapeHtml(line.text)}
            </div>`
        ).join('');

        this.elements.lyricsContainer.innerHTML = lyricsHTML;

        // Show source
        if (this.elements.ytmusicLyricsSource && lyricsData.source) {
            this.elements.ytmusicLyricsSource.textContent = `Source: ${lyricsData.source}`;
        }
    }

    /**
     * Display static lyrics
     */
    displayStaticLyrics(lyricsData) {
        if (!this.elements.lyricsContainer || !lyricsData.text) return;

        this.elements.lyricsContainer.innerHTML =
            `<div class="ytmusic-static-lyrics">${this.escapeHtml(lyricsData.text)}</div>`;

        // Show source
        if (this.elements.ytmusicLyricsSource && lyricsData.source) {
            this.elements.ytmusicLyricsSource.textContent = `Source: ${lyricsData.source}`;
        }
    }

    /**
     * Show no lyrics state
     */
    showNoLyricsState() {
        if (this.elements.lyricsContainer) {
            this.elements.lyricsContainer.innerHTML =
                '<div class="no-lyrics">No lyrics available for this song</div>';
        }
    }

    /**
     * Start lyrics synchronization
     */
    startLyricsSync() {
        if (this.lyricsInterval) {
            clearInterval(this.lyricsInterval);
        }

        this.lyricsInterval = setInterval(() => {
            this.updateLyricsHighlight();
        }, POPUP_CONFIG.TIMING.LYRICS_SYNC_INTERVAL);
    }

    /**
     * Update lyrics highlight based on current time
     */
    updateLyricsHighlight() {
        if (!this.currentSession?.lyricsData ||
            !this.currentSession?.playbackState ||
            this.currentSession.lyricsData.type !== 'synced') {
            return;
        }

        const { currentTime } = this.currentSession.playbackState;
        const lines = this.elements.lyricsContainer?.querySelectorAll('.ytmusic-lyrics-line');

        if (!lines) return;

        let activeIndex = -1;

        // Find current line
        for (let i = 0; i < lines.length; i++) {
            const lineTime = parseFloat(lines[i].dataset.time);
            if (currentTime >= lineTime) {
                activeIndex = i;
            } else {
                break;
            }
        }

        // Update highlight
        if (activeIndex !== this.activeLineIndex) {
            // Remove previous highlight
            lines.forEach(line => line.classList.remove('active'));

            // Add new highlight
            if (activeIndex >= 0 && lines[activeIndex]) {
                lines[activeIndex].classList.add('active');

                // Auto-scroll to active line
                lines[activeIndex].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }

            this.activeLineIndex = activeIndex;
        }
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Format time in MM:SS or HH:MM:SS format
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Apply theme color to UI
     */
    applyThemeColor(color) {
        document.documentElement.style.setProperty('--theme-color', color);
        document.documentElement.style.setProperty('--theme-color-rgb', this.hexToRgb(color));
    }

    /**
     * Convert hex color to RGB
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ?
            `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
            '255, 107, 53';
    }

    /**
     * Start heartbeat monitoring
     */
    startHeartbeatMonitoring() {
        this.heartbeatCheckInterval = setInterval(() => {
            if (this.connectionState === 'connected' && this.lastHeartbeat) {
                const timeSinceHeartbeat = Date.now() - this.lastHeartbeat;
                if (timeSinceHeartbeat > POPUP_CONFIG.TIMING.HEARTBEAT_TIMEOUT) {
                    console.warn('🎵 Heartbeat timeout detected');
                    this.handleConnectionLoss();
                }
            }
        }, POPUP_CONFIG.TIMING.HEARTBEAT_TIMEOUT / 2);
    }

    /**
     * Update session switcher UI
     */
    updateSessionSwitcher() {
        // Implementation for session switching UI if needed
        // This would show multiple tabs if there are multiple sessions
    }

    // ============================================================================
    // PLAYBACK CONTROL
    // ============================================================================

    /**
     * Send playback command to active session
     */
    async sendPlaybackCommand(command) {
        try {
            if (!this.activeSessionId) {
                throw new Error('No active session');
            }

            await this.sendToCoordinator('sendToSession', {
                tabId: this.activeSessionId,
                message: {
                    action: command === 'playPause' ? 'playPause' : 'playbackControl',
                    data: { command }
                }
            });

            console.log('✅ Playback command sent:', command);
        } catch (error) {
            console.error('❌ Failed to send playback command:', error);
        }
    }

    /**
     * Set up all event listeners (simplified for coordinator architecture)
     */
    setupEventListeners() {
        // Header controls
        if (this.elements.settingsButton) {
            this.elements.settingsButton.addEventListener('click', () => this.toggleSettings());
        }
        if (this.elements.openTabButton) {
            this.elements.openTabButton.addEventListener('click', () => this.handleOpenInTab());
        }
        if (this.elements.masterToggle) {
            this.elements.masterToggle.addEventListener('click', () => this.toggleExtension());
        }

        // Playback controls (main view)
        if (this.elements.prevButton) {
            this.elements.prevButton.addEventListener('click', () => this.sendPlaybackCommand('previous'));
        }
        if (this.elements.playPauseButton) {
            this.elements.playPauseButton.addEventListener('click', () => this.sendPlaybackCommand('playPause'));
        }
        if (this.elements.nextButton) {
            this.elements.nextButton.addEventListener('click', () => this.sendPlaybackCommand('next'));
        }

        // Playback controls (mini view)
        if (this.elements.miniPrevButton) {
            this.elements.miniPrevButton.addEventListener('click', () => this.sendPlaybackCommand('previous'));
        }
        if (this.elements.miniPlayPauseButton) {
            this.elements.miniPlayPauseButton.addEventListener('click', () => this.sendPlaybackCommand('playPause'));
        }
        if (this.elements.miniNextButton) {
            this.elements.miniNextButton.addEventListener('click', () => this.sendPlaybackCommand('next'));
        }

        // Progress bar seeking
        this.elements.progressBar.addEventListener('click', (e) => this.handleProgressBarClick(e));
        this.elements.miniProgressBar.addEventListener('click', (e) => this.handleProgressBarClick(e));

        // Minimize/expand controls
        this.elements.minimizeButton.addEventListener('click', () => this.minimizePlayerWithAnimation());
        this.elements.expandButton.addEventListener('click', () => this.expandPlayerWithAnimation());

        // Double-click to toggle view
        this.elements.miniPlayerLayout.addEventListener('dblclick', () => this.togglePlayerView());
        this.elements.albumCoverContainer.addEventListener('dblclick', () => this.togglePlayerView());

        // Settings page controls
        this.elements.closeSettings.addEventListener('click', () => this.toggleSettings());
        this.elements.activeSongSelector.addEventListener('change', (e) => {
            this.handleSongSelectorChange(e.target.value);
        });
        this.elements.openTabBehavior.addEventListener('change', (e) => {
            this.settings.openTabBehavior = e.target.value;
            this.saveSettings();
        });

        this.elements.dynamicColorsToggle.addEventListener('click', () => {
            this.settings.DYNAMIC_THEME_COLORS = !this.settings.DYNAMIC_THEME_COLORS;
            this.updateToggleState(this.elements.dynamicColorsToggle, this.settings.DYNAMIC_THEME_COLORS);
            this.saveSettings();
            if (this.settings.DYNAMIC_THEME_COLORS && this.currentSong) {
                this.extractAndApplyThemeColor();
            }
        });

        // Auto scroll toggle
        this.elements.autoScrollToggle.addEventListener('click', () => {
            this.settings.AUTO_SCROLL = !this.settings.AUTO_SCROLL;
            this.updateToggleState(this.elements.autoScrollToggle, this.settings.AUTO_SCROLL);
            this.saveSettings();
        });

        // Popup font size slider (popup-specific, independent)
        this.elements.popupFontSizeSlider.addEventListener('input', (e) => {
            this.settings.popupFontSize = parseInt(e.target.value);
            this.elements.popupFontSizeValue.textContent = `${this.settings.popupFontSize}px`;
            this.applyLyricsStyles();
        });

        this.elements.popupFontSizeSlider.addEventListener('change', () => {
            this.saveSettings();
        });

        // Sync offset slider
        this.elements.syncOffsetSlider.addEventListener('input', (e) => {
            this.settings.SYNC_OFFSET = parseFloat(e.target.value);
            this.elements.syncOffsetValue.textContent = `${this.settings.SYNC_OFFSET.toFixed(1)}s`;
        });

        this.elements.syncOffsetSlider.addEventListener('change', () => {
            this.saveSettings();
        });

        // Highlight color picker
        this.elements.highlightColorPicker.addEventListener('input', (e) => {
            this.settings.HIGHLIGHT_COLOR = e.target.value; // Content.js format
            this.elements.highlightColorValue.textContent = e.target.value;
            if (!this.settings.DYNAMIC_THEME_COLORS) {
                this.applyThemeColor(e.target.value);
            }
        });

        this.elements.highlightColorPicker.addEventListener('change', () => {
            this.saveSettings();
        });

        // Settings buttons
        this.elements.exportSettings.addEventListener('click', () => this.exportSettings());
        this.elements.importSettings.addEventListener('click', () => this.importSettings());
        this.elements.clearCache.addEventListener('click', () => this.clearLyricsCache());
        this.elements.resetSettings.addEventListener('click', () => this.resetSettings());
        this.elements.githubLink.addEventListener('click', () => this.openGitHubPage());
        this.elements.reportIssue.addEventListener('click', () => this.reportIssue());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Add ripple effects to buttons
        this.addRippleEffects();

        // Add accessibility improvements
        this.addAccessibilityFeatures();

        // Add performance optimizations
        this.addPerformanceOptimizations();
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(event) {
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {
            return; // Don't handle shortcuts when typing in inputs
        }

        switch (event.key) {
            case ' ':
                event.preventDefault();
                this.sendPlaybackCommand('playPause');
                break;
            case 'ArrowLeft':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.switchToPreviousTab();
                } else {
                    this.sendPlaybackCommand('previous');
                }
                break;
            case 'ArrowRight':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.switchToNextTab();
                } else {
                    this.sendPlaybackCommand('next');
                }
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.minimizePlayer();
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.expandPlayer();
                break;
            case 'Escape':
                if (this.isSettingsOpen) {
                    this.toggleSettings();
                }
                break;
        }
    }

    /**
     * Set up color extraction canvas for dynamic theming
     */
    setupColorExtraction() {
        this.colorExtractionCanvas = document.createElement('canvas');
        this.colorExtractionCanvas.width = 1;
        this.colorExtractionCanvas.height = 1;
        this.colorExtractionContext = this.colorExtractionCanvas.getContext('2d');
    }

    /**
     * Load settings from storage
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get([
                POPUP_CONFIG.STORAGE.EXTENSION_ENABLED,
                POPUP_CONFIG.STORAGE.POPUP_SETTINGS,
                POPUP_CONFIG.STORAGE.THEME_COLORS,
                POPUP_CONFIG.STORAGE.OPEN_TAB_BEHAVIOR
            ]);

            // Load extension enabled state
            this.extensionEnabled = result[POPUP_CONFIG.STORAGE.EXTENSION_ENABLED] !== false;

            // Load popup settings
            if (result[POPUP_CONFIG.STORAGE.POPUP_SETTINGS]) {
                this.settings = { ...this.settings, ...result[POPUP_CONFIG.STORAGE.POPUP_SETTINGS] };
            }

            // Load theme color
            if (result[POPUP_CONFIG.STORAGE.THEME_COLORS]) {
                this.currentThemeColor = result[POPUP_CONFIG.STORAGE.THEME_COLORS].current || POPUP_CONFIG.THEME.DEFAULT_COLOR;
            }

            // Defer UI update if elements aren't ready yet
            if (this.elements && this.elements.masterToggle) {
                this.updateUIFromSettings();
            } else {
                // Retry after a short delay
                setTimeout(() => {
                    this.updateUIFromSettings();
                }, 100);
            }

            this.applyThemeColor(this.currentThemeColor);

        } catch (error) {
            console.error('❌ Error loading settings:', error);
        }
    }

    /**
     * Save settings to storage and sync with background and content scripts
     */
    async saveSettings() {
        try {
            await chrome.storage.sync.set({
                [POPUP_CONFIG.STORAGE.EXTENSION_ENABLED]: this.extensionEnabled,
                [POPUP_CONFIG.STORAGE.POPUP_SETTINGS]: this.settings,
                [POPUP_CONFIG.STORAGE.THEME_COLORS]: {
                    current: this.currentThemeColor
                }
            });

            // Sync with background script
            await this.syncSettingsWithBackground();

            // Sync with content scripts
            await this.syncSettingsWithContentScripts();

        } catch (error) {
            console.error('❌ Error saving settings:', error);
        }
    }

    /**
     * Sync settings with all content scripts
     */
    async syncSettingsWithContentScripts() {
        if (this.musicTabs.length === 0) return;

        // Extract only the synced settings (those that exist in content.js)
        const syncedSettings = {
            SYNC_OFFSET: this.settings.SYNC_OFFSET,
            AUTO_SCROLL: this.settings.AUTO_SCROLL,
            FONT_SIZE: this.settings.FONT_SIZE,
            HIGHLIGHT_COLOR: this.settings.HIGHLIGHT_COLOR,
            DYNAMIC_THEME_COLORS: this.settings.DYNAMIC_THEME_COLORS
        };

        // Determine scope based on current song selector
        let scope = 'global';
        const selector = this.elements.activeSongSelector;
        if (selector && selector.value && selector.value.startsWith('tab-')) {
            scope = 'song'; // Save for specific song
        }

        // Send to all music tabs with scope information
        const syncPromises = this.musicTabs.map(async (tab) => {
            try {
                await this.sendMessageToTab(tab.id, 'updateSettings', {
                    settings: syncedSettings,
                    scope: scope
                });
                console.log(`🎵 Synced settings with tab ${tab.id} (scope: ${scope})`);
            } catch (error) {
                console.warn(`Could not sync settings with tab ${tab.id}:`, error.message);
            }
        });

        await Promise.all(syncPromises);
    }

    /**
     * Update UI elements from current settings
     */
    updateUIFromSettings() {
        try {
            // Only update if elements are cached and available
            if (!this.elements || !this.elements.masterToggle) {
                console.warn('Elements not yet cached, deferring UI update');
                return;
            }

            // Update master toggle
            if (this.elements.masterToggle) {
                this.updateToggleState(this.elements.masterToggle, this.extensionEnabled);
            }

            // Update popup-specific settings
            if (this.elements.openTabBehavior) {
                this.elements.openTabBehavior.value = this.settings.openTabBehavior;
            }

            if (this.elements.popupFontSizeSlider) {
                this.elements.popupFontSizeSlider.value = this.settings.popupFontSize;
                if (this.elements.popupFontSizeValue) {
                    this.elements.popupFontSizeValue.textContent = `${this.settings.popupFontSize}px`;
                }
            }

            // Update synced settings (matching content.js)
            if (this.elements.dynamicColorsToggle) {
                this.updateToggleState(this.elements.dynamicColorsToggle, this.settings.DYNAMIC_THEME_COLORS);
            }

            if (this.elements.autoScrollToggle) {
                this.updateToggleState(this.elements.autoScrollToggle, this.settings.AUTO_SCROLL);
            }

            if (this.elements.syncOffsetSlider) {
                this.elements.syncOffsetSlider.value = this.settings.SYNC_OFFSET;
                if (this.elements.syncOffsetValue) {
                    this.elements.syncOffsetValue.textContent = `${this.settings.SYNC_OFFSET}s`;
                }
            }

            if (this.elements.highlightColorPicker) {
                this.elements.highlightColorPicker.value = this.settings.HIGHLIGHT_COLOR;
                if (this.elements.highlightColorValue) {
                    this.elements.highlightColorValue.textContent = this.settings.HIGHLIGHT_COLOR;
                }
            }

            // Apply lyrics styles
            this.applyLyricsStyles();

        } catch (error) {
            console.error('❌ Error updating UI from settings:', error);
        }
    }

    /**
     * Update toggle switch visual state
     */
    updateToggleState(toggleElement, isActive) {
        if (!toggleElement || !toggleElement.classList) {
            console.warn('Invalid toggle element provided');
            return;
        }

        try {
            if (isActive) {
                toggleElement.classList.add('active');
            } else {
                toggleElement.classList.remove('active');
            }
        } catch (error) {
            console.error('❌ Error updating toggle state:', error);
        }
    }

    /**
     * Apply theme color to CSS custom properties
     */
    applyThemeColor(color) {
        if (!color) {
            console.warn('❌ No color provided to applyThemeColor');
            return;
        }

        console.log('🎨 Applying theme color in popup:', color);

        const root = document.documentElement;

        // Parse color to RGB
        const rgb = this.hexToRgb(color);
        if (!rgb) {
            console.warn('❌ Invalid color format:', color);
            return;
        }

        const { r, g, b } = rgb;

        // Update CSS custom properties
        root.style.setProperty('--theme-color', color);
        root.style.setProperty('--theme-color-light', this.lightenColor(color, 20));
        root.style.setProperty('--theme-color-dark', this.darkenColor(color, 20));

        // Alpha variants
        root.style.setProperty('--theme-color-alpha-10', `rgba(${r}, ${g}, ${b}, 0.1)`);
        root.style.setProperty('--theme-color-alpha-15', `rgba(${r}, ${g}, ${b}, 0.15)`);
        root.style.setProperty('--theme-color-alpha-20', `rgba(${r}, ${g}, ${b}, 0.2)`);
        root.style.setProperty('--theme-color-alpha-30', `rgba(${r}, ${g}, ${b}, 0.3)`);
        root.style.setProperty('--theme-color-alpha-40', `rgba(${r}, ${g}, ${b}, 0.4)`);
        root.style.setProperty('--theme-color-alpha-50', `rgba(${r}, ${g}, ${b}, 0.5)`);
        root.style.setProperty('--theme-color-glow', `rgba(${r}, ${g}, ${b}, 0.7)`);

        // Also update the lyrics highlight color
        root.style.setProperty('--lyrics-highlight-color', color);

        this.currentThemeColor = color;

        console.log('✅ Theme color applied successfully');
    }

    /**
     * Apply lyrics styling based on current settings (matches content.js)
     */
    applyLyricsStyles() {
        try {
            // Check if lyrics elements exist
            if (!this.elements || !this.elements.lyricsLines) {
                return; // Elements not ready yet
            }

            // Apply styles to both old and new CSS classes for compatibility
            const lyricsLines = this.elements.lyricsLines.querySelectorAll('.lyrics-line, .ytmusic-lyrics-line');
            lyricsLines.forEach(line => {
                if (line && line.style) {
                    // Use popup-specific font size
                    line.style.fontSize = `${this.settings.popupFontSize || 13}px`;
                }
            });

            // Apply styles to static lyrics as well
            const staticLyrics = this.elements.lyricsLines.querySelectorAll('.ytmusic-static-lyrics');
            staticLyrics.forEach(element => {
                if (element && element.style) {
                    // Use popup-specific font size
                    element.style.fontSize = `${this.settings.popupFontSize || 13}px`;
                }
            });

            console.log('🎵 Applied lyrics styles:', {
                popupFontSize: this.settings.popupFontSize
            });
        } catch (error) {
            console.warn('Could not apply lyrics styles:', error);
        }
    }

    /**
     * Discover YouTube Music tabs and check which ones have active music
     */
    async discoverMusicTabs() {
        try {
            console.log('🎵 Discovering YouTube Music tabs...');
            const tabs = await chrome.tabs.query({ url: '*://music.youtube.com/*' });
            const potentialTabs = tabs.filter(tab => tab.url && tab.url.includes('music.youtube.com'));

            console.log(`🎵 Found ${potentialTabs.length} YouTube Music tabs`);

            // Check each tab for active music
            this.musicTabs = [];
            const tabCheckPromises = potentialTabs.map(async (tab) => {
                try {
                    const response = await this.sendMessageToTab(tab.id, 'healthCheck');
                    if (response && response.song && response.song.title) {
                        // Tab has active music
                        tab.isHealthy = true;
                        tab.lastHealthCheck = Date.now();
                        tab.songInfo = response.song;
                        tab.playbackState = response.playbackState;

                        // Store the source tab ID for message matching
                        if (response.sourceTabId) {
                            tab.sourceTabId = response.sourceTabId;
                        }

                        this.musicTabs.push(tab);
                        console.log(`🎵 Tab ${tab.id} has active music: ${response.song.title}`);
                    }
                } catch (error) {
                    console.log(`🎵 Tab ${tab.id} has no active music or extension not loaded`);
                }
            });

            await Promise.all(tabCheckPromises);

            // Sort tabs by most recently active (playing > paused > no music)
            this.musicTabs.sort((a, b) => {
                const aPlaying = a.playbackState?.isPlaying ? 2 : 1;
                const bPlaying = b.playbackState?.isPlaying ? 2 : 1;
                if (aPlaying !== bPlaying) return bPlaying - aPlaying;

                // If same play state, prefer active tab
                if (a.active && !b.active) return -1;
                if (!a.active && b.active) return 1;

                return 0;
            });

            // Update tab navigation visibility
            const hasMultipleTabs = this.musicTabs.length > 1;
            this.elements.prevTab.classList.toggle('visible', hasMultipleTabs);
            this.elements.nextTab.classList.toggle('visible', hasMultipleTabs);

            if (this.musicTabs.length > 0) {
                // Select the most relevant tab (playing > active > first)
                this.currentTabIndex = 0;
                console.log(`🎵 Selected tab ${this.musicTabs[0].id} as current`);
                this.updateCurrentSong();
            } else {
                this.showNoMusicTabsMessage();
            }

        } catch (error) {
            console.error('❌ Error discovering music tabs:', error);
            this.showError('Failed to find YouTube Music tabs');
        }
    }

    /**
     * Send message to a specific tab
     */
    async sendMessageToTab(tabId, action, data = {}) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, {
                action,
                data,
                timestamp: Date.now()
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Switch to previous tab
     */
    async switchToPreviousTab() {
        if (this.musicTabs.length <= 1) return;

        const oldIndex = this.currentTabIndex;
        this.currentTabIndex = (this.currentTabIndex - 1 + this.musicTabs.length) % this.musicTabs.length;

        console.log(`🎵 Switching from tab ${oldIndex} to ${this.currentTabIndex}`);
        this.updateCurrentSong();
        this.animateTabSwitch('left');
    }

    /**
     * Switch to next tab
     */
    async switchToNextTab() {
        if (this.musicTabs.length <= 1) return;

        const oldIndex = this.currentTabIndex;
        this.currentTabIndex = (this.currentTabIndex + 1) % this.musicTabs.length;

        console.log(`🎵 Switching from tab ${oldIndex} to ${this.currentTabIndex}`);
        this.updateCurrentSong();
        this.animateTabSwitch('right');
    }

    /**
     * Animate tab switch with visual feedback
     */
    animateTabSwitch(direction) {
        const songInfo = this.elements.songInfo;
        if (!songInfo) return;

        // Add slide animation class
        songInfo.classList.add(`slide-${direction}`);

        // Remove animation class after animation completes
        setTimeout(() => {
            songInfo.classList.remove(`slide-${direction}`);
        }, 300);
    }

    /**
     * Refresh tab discovery (useful when tabs are closed/opened) - with throttling
     */
    async refreshTabs() {
        // Throttle tab refresh to prevent excessive calls
        const now = Date.now();
        if (this.lastTabRefresh && now - this.lastTabRefresh < 10000) {
            return; // Skip if less than 10 seconds since last refresh
        }
        this.lastTabRefresh = now;

        console.log('🎵 Refreshing tab discovery...');
        const currentTabId = this.musicTabs[this.currentTabIndex]?.id;

        await this.discoverMusicTabs();

        // Try to maintain current tab if it still exists
        if (currentTabId) {
            const newIndex = this.musicTabs.findIndex(tab => tab.id === currentTabId);
            if (newIndex !== -1) {
                this.currentTabIndex = newIndex;
            }
        }
    }

    /**
     * Toggle extension on/off
     */
    async toggleExtension() {
        this.extensionEnabled = !this.extensionEnabled;
        this.updateToggleState(this.elements.masterToggle, this.extensionEnabled);
        await this.saveSettings();

        // Send message to all music tabs
        for (const tab of this.musicTabs) {
            try {
                await chrome.tabs.sendMessage(tab.id, {
                    action: 'toggleExtension',
                    enabled: this.extensionEnabled
                });
            } catch (error) {
                console.warn('Could not send toggle message to tab:', tab.id);
            }
        }
    }

    /**
     * Toggle settings page
     */
    toggleSettings() {
        this.isSettingsOpen = !this.isSettingsOpen;
        if (this.isSettingsOpen) {
            this.updateSongSelector();
            this.updateUIFromSettings();
            this.elements.settingsPage.style.animation = 'fadeSlideIn 0.3s ease-out forwards';
            this.elements.settingsPage.classList.add('visible');
        } else {
            this.elements.settingsPage.style.animation = 'fadeSlideOut 0.3s ease-out forwards';
            setTimeout(() => {
                this.elements.settingsPage.classList.remove('visible');
            }, 300); // Match animation duration
        }
    }

    /**
     * Update the song selector dropdown with available songs
     */
    updateSongSelector() {
        const selector = this.elements.activeSongSelector;

        // Clear existing options except global
        selector.innerHTML = '<option value="global">Global Settings (All Songs)</option>';

        // Add options for each active music tab
        this.musicTabs.forEach((tab, index) => {
            if (tab.songInfo && tab.songInfo.title) {
                const option = document.createElement('option');
                option.value = `tab-${tab.id}`;
                option.textContent = `${tab.songInfo.title} - ${tab.songInfo.artist}`;
                selector.appendChild(option);

                // Select current tab if it matches
                if (index === this.currentTabIndex) {
                    option.selected = true;
                }
            }
        });

        // Update current song info display
        this.updateCurrentSongInfo();
    }

    /**
     * Handle song selector change
     */
    handleSongSelectorChange(value) {
        console.log('🎵 Song selector changed to:', value);

        if (value === 'global') {
            // Load global settings
            this.loadGlobalSettings();
        } else if (value.startsWith('tab-')) {
            // Load settings for specific tab
            const tabId = parseInt(value.replace('tab-', ''));
            this.loadTabSettings(tabId);
        }

        this.updateCurrentSongInfo();
    }

    /**
     * Update current song info display
     */
    updateCurrentSongInfo() {
        const selector = this.elements.activeSongSelector;
        const selectedValue = selector.value;
        const songInfoElement = this.elements.currentSongInfo;

        if (selectedValue === 'global') {
            songInfoElement.querySelector('.song-title').textContent = 'Global Settings';
            songInfoElement.querySelector('.song-artist').textContent = 'These settings apply to all songs by default';
        } else if (selectedValue.startsWith('tab-')) {
            const tabId = parseInt(selectedValue.replace('tab-', ''));
            const tab = this.musicTabs.find(t => t.id === tabId);

            if (tab && tab.songInfo) {
                songInfoElement.querySelector('.song-title').textContent = tab.songInfo.title;
                songInfoElement.querySelector('.song-artist').textContent = `by ${tab.songInfo.artist}`;
            } else {
                songInfoElement.querySelector('.song-title').textContent = 'Song Not Found';
                songInfoElement.querySelector('.song-artist').textContent = 'This song may no longer be available';
            }
        }
    }

    /**
     * Load global settings
     */
    async loadGlobalSettings() {
        try {
            // Load global settings from storage
            const result = await chrome.storage.sync.get([POPUP_CONFIG.STORAGE.POPUP_SETTINGS]);
            if (result[POPUP_CONFIG.STORAGE.POPUP_SETTINGS]) {
                this.settings = { ...POPUP_CONFIG.DEFAULTS, ...result[POPUP_CONFIG.STORAGE.POPUP_SETTINGS] };
                this.updateUIFromSettings();
                console.log('🎵 Loaded global settings');
            }
        } catch (error) {
            console.error('❌ Error loading global settings:', error);
        }
    }

    /**
     * Load settings for a specific tab
     */
    async loadTabSettings(tabId) {
        try {
            // Get settings from the content script
            const response = await this.sendMessageToTab(tabId, 'getSettings');
            if (response && response.settings) {
                // Merge synced settings directly (they use the same format now)
                const syncedSettings = {
                    SYNC_OFFSET: response.settings.SYNC_OFFSET,
                    AUTO_SCROLL: response.settings.AUTO_SCROLL,
                    FONT_SIZE: response.settings.FONT_SIZE,
                    HIGHLIGHT_COLOR: response.settings.HIGHLIGHT_COLOR,
                    DYNAMIC_THEME_COLORS: response.settings.DYNAMIC_THEME_COLORS
                };
                this.settings = { ...this.settings, ...syncedSettings };
                this.updateUIFromSettings();
                console.log('🎵 Loaded settings for tab:', tabId);
            }
        } catch (error) {
            console.error('❌ Error loading tab settings:', error);
            // Fallback to global settings
            this.loadGlobalSettings();
        }
    }

    /**
     * Handle open in tab button
     */
    async handleOpenInTab() {
        if (this.settings.openTabBehavior === 'popup') {
            // Create floating popup window
            try {
                await chrome.windows.create({
                    url: chrome.runtime.getURL('popup.html'),
                    type: 'popup',
                    width: 400,
                    height: 600,
                    focused: true
                });
            } catch (error) {
                console.error('❌ Error creating popup window:', error);
                this.showError('Failed to create popup window');
            }
        } else {
            // Open in new tab
            try {
                await chrome.tabs.create({
                    url: chrome.runtime.getURL('popup.html')
                });
            } catch (error) {
                console.error('❌ Error opening in new tab:', error);
                this.showError('Failed to open in new tab');
            }
        }
    }

    /**
     * Update current song information
     */
    async updateCurrentSong() {
        if (this.musicTabs.length === 0) {
            this.showNoMusicTabsMessage();
            return;
        }

        const currentTab = this.musicTabs[this.currentTabIndex];
        if (!currentTab) return;

        try {
            // Use cached song info if available and recent
            if (currentTab.songInfo && currentTab.lastHealthCheck &&
                (Date.now() - currentTab.lastHealthCheck) < 5000) {
                this.handleSongChange(currentTab.songInfo);
                // REMOVED: updatePlaybackState call to prevent conflicts
                return;
            }

            // Request fresh data from content script
            const response = await this.sendContentMessage('getFullState');
            if (response && response.song) {
                // Update cached data
                currentTab.songInfo = response.song;
                currentTab.playbackState = response.playbackState;
                currentTab.lastHealthCheck = Date.now();

                this.handleSongChange(response.song);
                // REMOVED: updatePlaybackState call to prevent conflicts with message handlers
                if (response.lyricsData) {
                    this.handleLyricsDataChange(response.lyricsData);
                }
                if (response.settings) {
                    this.handleContentSettingsChange(response.settings);
                }
            } else {
                this.showNoSongMessage();
            }
        } catch (error) {
            console.warn('Could not get song info from tab:', currentTab.id, error.message);
            this.handleTabConnectionLoss(currentTab.id);
        }
    }

    /**
     * Update song display in UI
     */
    updateSongDisplay() {
        if (!this.currentSong) return;

        // Update main view
        this.elements.songTitle.textContent = this.currentSong.title;
        this.elements.songArtist.textContent = this.currentSong.artist;

        // Update mini view
        this.elements.miniSongTitle.textContent = this.currentSong.title;
        this.elements.miniSongArtist.textContent = this.currentSong.artist;

        // Update album cover if available
        if (this.currentSong.albumCover) {
            this.elements.albumCover.src = this.currentSong.albumCover;
            this.elements.albumCover.classList.remove('hidden');
            this.elements.albumCoverPlaceholder.style.display = 'none';

            this.elements.miniAlbumCover.src = this.currentSong.albumCover;
            this.elements.miniAlbumCover.classList.remove('hidden');
            this.elements.miniCoverPlaceholder.style.display = 'none';
        } else {
            this.elements.albumCover.classList.add('hidden');
            this.elements.albumCoverPlaceholder.style.display = 'flex';

            this.elements.miniAlbumCover.classList.add('hidden');
            this.elements.miniCoverPlaceholder.style.display = 'flex';
        }
    }

    /**
     * Handle continuous playback state updates from content script
     * @param {Object} data - Playback state data
     */
    handlePlaybackStateUpdate(data) {
        try {
            if (!data) return;

            // Update current time and duration from content script
            if (data.currentTime !== undefined) {
                this.currentTime = data.currentTime;
            }
            if (data.duration !== undefined) {
                this.duration = data.duration;
            }
            if (data.isPlaying !== undefined) {
                this.isPlaying = data.isPlaying;
            }

            // Update UI elements
            this.updateProgressBars();
            this.updateTimeDisplays();
            this.updatePlayPauseButtons();

            // Update lyrics highlighting if we have lyrics
            if (this.lyricsData && this.lyricsData.type === 'synced') {
                this.updateLyricsHighlight();
            }

        } catch (error) {
            console.error('❌ Error handling playback state update:', error);
        }
    }

    /**
     * Update playback state - ONLY buttons, NO time/progress updates
     */
    updatePlaybackState(state) {
        this.isPlaying = state.isPlaying;
        // Store time data but don't update UI here to prevent conflicts
        if (state.currentTime !== undefined) this.currentTime = state.currentTime;
        if (state.duration !== undefined) this.duration = state.duration;

        // Update play/pause buttons ONLY
        this.updatePlayPauseButtons();

        // NO progress bars or time display updates here - handled by single authoritative method
    }

    /**
     * Update play/pause button states
     */
    updatePlayPauseButtons() {
        this.elements.playIcon.classList.toggle('hidden', this.isPlaying);
        this.elements.pauseIcon.classList.toggle('hidden', !this.isPlaying);
        this.elements.miniPlayIcon.classList.toggle('hidden', this.isPlaying);
        this.elements.miniPauseIcon.classList.toggle('hidden', !this.isPlaying);
    }

    /**
     * Update progress bars based on current time and duration
     */
    updateProgressBars() {
        if (this.duration && this.duration > 0) {
            const progress = Math.max(0, Math.min(100, (this.currentTime / this.duration) * 100));

            if (this.elements.progressFill) {
                this.elements.progressFill.style.width = `${progress}%`;
            }
            if (this.elements.miniProgressFill) {
                this.elements.miniProgressFill.style.width = `${progress}%`;
            }
        }
    }

    /**
     * Update time displays
     */
    updateTimeDisplays() {
        if (this.elements.currentTime) {
            this.elements.currentTime.textContent = this.formatTime(this.currentTime || 0);
        }
        if (this.elements.totalTime) {
            this.elements.totalTime.textContent = this.formatTime(this.duration || 0);
        }
    }

    /**
     * Format time in seconds to MM:SS format
     * @param {number} seconds - Time in seconds
     * @returns {string} Formatted time string
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Update lyrics highlighting based on current time
     */
    updateLyricsHighlight() {
        if (!this.lyricsData || this.lyricsData.type !== 'synced' || !this.lyricsData.lines) {
            return;
        }

        const currentTime = this.currentTime || 0;
        const syncOffset = this.settings.syncOffset || 0;
        const adjustedTime = currentTime + syncOffset;

        // Find the active line
        let activeIndex = -1;
        for (let i = 0; i < this.lyricsData.lines.length; i++) {
            const line = this.lyricsData.lines[i];
            const nextLine = this.lyricsData.lines[i + 1];

            if (adjustedTime >= line.time && (!nextLine || adjustedTime < nextLine.time)) {
                activeIndex = i;
                break;
            }
        }

        // Update highlighting if active line changed
        if (activeIndex !== this.activeLineIndex) {
            this.activeLineIndex = activeIndex;
            this.updateLyricsDisplay();
        }
    }

    /**
     * Update lyrics display with highlighting
     */
    updateLyricsDisplay() {
        if (!this.elements.lyricsLines) return;

        const lines = this.elements.lyricsLines.querySelectorAll('.lyrics-line, .ytmusic-lyrics-line');
        lines.forEach((line, index) => {
            line.classList.remove('active', 'passed', 'upcoming');

            if (index === this.activeLineIndex) {
                line.classList.add('active');
                // Auto-scroll to active line if enabled
                if (this.settings.autoScroll) {
                    line.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            } else if (index < this.activeLineIndex) {
                line.classList.add('passed');
            } else {
                line.classList.add('upcoming');
            }
        });
    }

    /**
     * Send playback command to current tab
     */
    async sendPlaybackCommand(command) {
        try {
            const response = await this.sendContentMessage('playbackControl', { command });
            console.log('✅ Playback command sent:', command);

            // Immediately request updated state
            setTimeout(() => {
                this.performHealthCheck();
            }, 100);
        } catch (error) {
            console.error('❌ Failed to send playback command:', command, error);
            this.showError(`Failed to ${command} playback`);
        }
    }

    /**
     * Minimize player view with smooth animation
     */
    minimizePlayer() {
        if (this.isMinimized) return;

        this.isMinimized = true;
        this.elements.popupMain.classList.add('minimized');

        // Add visual feedback
        this.elements.minimizeButton.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.elements.minimizeButton.style.transform = '';
        }, 150);

        // Update button states
        this.updateMinimizeExpandButtons();

        // Auto-focus on lyrics if available
        if (this.lyricsData) {
            setTimeout(() => {
                this.scrollToActiveLyric();
            }, POPUP_CONFIG.TIMING.TRANSITION_DURATION);
        }
    }

    /**
     * Expand player view with smooth animation
     */
    expandPlayer() {
        if (!this.isMinimized) return;

        this.isMinimized = false;
        this.elements.popupMain.classList.remove('minimized');

        // Add visual feedback
        this.elements.expandButton.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.elements.expandButton.style.transform = '';
        }, 150);

        // Update button states
        this.updateMinimizeExpandButtons();
    }

    /**
     * Update minimize/expand button states
     */
    updateMinimizeExpandButtons() {
        // Update button visibility and states based on current view
        this.elements.minimizeButton.style.opacity = this.isMinimized ? '0.5' : '1';
        this.elements.expandButton.style.opacity = this.isMinimized ? '1' : '0.5';
    }

    /**
     * Scroll to the currently active lyric line
     */
    scrollToActiveLyric() {
        if (!this.lyricsData || this.activeLineIndex < 0) return;

        const activeLine = this.elements.lyricsLines.querySelector('.lyrics-line.active');
        if (activeLine) {
            activeLine.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    /**
     * Toggle between minimized and expanded views
     */
    togglePlayerView() {
        if (this.isMinimized) {
            this.expandPlayer();
        } else {
            this.minimizePlayer();
        }
    }

    /**
     * Fetch lyrics for current song with enhanced error handling
     */
    fetchLyrics() {
        if (!this.currentSong) return;

        // Show loading state
        this.elements.lyricsLoading.classList.remove('hidden');
        this.elements.lyricsError.classList.add('hidden');
        this.elements.lyricsLines.innerHTML = '';

        // Send request to background script with timeout
        const timeoutId = setTimeout(() => {
            this.showLyricsError('Request timed out. Please try again.');
        }, 15000); // 15 second timeout

        chrome.runtime.sendMessage({
            action: 'fetchLyrics',
            song: this.currentSong,
            timestamp: Date.now()
        }, (response) => {
            clearTimeout(timeoutId);
            this.elements.lyricsLoading.classList.add('hidden');

            if (chrome.runtime.lastError) {
                console.error('❌ Background script error:', chrome.runtime.lastError);
                this.showLyricsError('Failed to communicate with background service');
                return;
            }

            if (response && response.lyrics) {
                this.lyricsData = response.lyrics;
                this.displayLyrics();
                console.log('✅ Lyrics loaded successfully');
            } else {
                this.showLyricsError(response?.error || 'No lyrics found');
            }
        });
    }

    /**
     * Set up real-time communication with content scripts
     */
    setupRealtimeCommunication() {
        // Listen for messages from content scripts and background
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleIncomingMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Set up health check with active tab (less frequent)
        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, POPUP_CONFIG.TIMING.UPDATE_INTERVAL);

        // REMOVED: High-frequency playback sync - causing conflicts
        // Time updates will come from content script messages only

        // Set up tab monitoring
        this.setupTabMonitoring();
    }

    /**
     * Set up tab monitoring for closed/refreshed tabs
     */
    setupTabMonitoring() {
        // Listen for tab updates (refresh, navigation)
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (this.musicTabs.some(musicTab => musicTab.id === tabId)) {
                if (changeInfo.status === 'loading') {
                    console.log(`🔄 Tab ${tabId} is refreshing/navigating`);
                    this.handleTabRefresh(tabId);
                } else if (changeInfo.status === 'complete' && tab.url && tab.url.includes('music.youtube.com')) {
                    console.log(`✅ Tab ${tabId} finished loading`);
                    // Give the content script time to initialize
                    setTimeout(() => {
                        this.attemptTabRecovery(tabId);
                    }, 1000);
                }
            }
        });

        // Listen for tab removal
        chrome.tabs.onRemoved.addListener((tabId) => {
            if (this.musicTabs.some(musicTab => musicTab.id === tabId)) {
                console.log(`🗑️ Tab ${tabId} was closed`);
                this.removeTabFromList(tabId);
            }
        });

        // Periodic tab discovery to find new YouTube Music tabs (reduced frequency)
        setInterval(() => {
            this.refreshTabs();
        }, 60000); // Every 60 seconds (reduced from 30)
    }

    /**
     * Handle tab refresh/navigation
     */
    handleTabRefresh(tabId) {
        const tabIndex = this.musicTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex !== -1) {
            // Mark tab as temporarily unhealthy during refresh
            this.musicTabs[tabIndex].isHealthy = false;
            this.musicTabs[tabIndex].isRefreshing = true;

            // If this is the current tab, show loading state
            if (tabIndex === this.currentTabIndex) {
                this.showConnectionError();
            }
        }
    }

    /**
     * Handle all incoming messages (content scripts and background)
     */
    handleIncomingMessage(message, sender, sendResponse) {
        try {
            // Handle messages from content scripts
            if (sender.tab && this.musicTabs.some(tab => tab.id === sender.tab.id)) {
                this.handleContentScriptMessage(message, sender, sendResponse);
            }
            // Handle messages sent directly from content scripts via runtime.sendMessage (no sender.tab)
            else if (message.fromContent === true) {
                // For direct messages without sender.tab, we need to find the tab based on musicTabs
                // Create a synthetic sender object with tab info
                const syntheticSender = {
                    tab: {
                        id: this.findTabIdFromMessage(message)
                    }
                };

                // Process with our synthetic sender
                this.handleContentScriptMessage(message, syntheticSender, sendResponse);
            }
            // Handle messages from background script
            else if (!sender.tab) {
                this.handleBackgroundMessage(message, sendResponse);
            }
        } catch (error) {
            console.error('❌ Error handling incoming message:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * Handle messages from content scripts
     */
    handleContentScriptMessage(message, sender, sendResponse) {
        const isCurrentTab = this.isCurrentTab(sender.tab.id);

        switch (message.action) {
            case 'songChanged':
                if (isCurrentTab) {
                    this.handleSongChange(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'playbackStateChanged':
                if (isCurrentTab) {
                    this.updatePlaybackState(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'playbackStateUpdate':
                if (isCurrentTab) {
                    this.handlePlaybackStateUpdate(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'lyricsDataChanged':
                if (isCurrentTab) {
                    this.handleLyricsDataChange(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'currentTimeUpdate':
                if (isCurrentTab) {
                    this.handleCurrentTimeUpdate(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'themeColorExtracted':
                if (isCurrentTab) {
                    // Always apply theme color, regardless of dynamic colors setting
                    this.applyThemeColor(message.data.color);
                    sendResponse({ success: true });
                }
                break;

            case 'settingsChanged':
                if (isCurrentTab) {
                    this.handleContentSettingsChange(message.data);
                    sendResponse({ success: true });
                }
                break;

            case 'healthCheckResponse':
                this.handleHealthCheckResponse(sender.tab.id, message.data);
                sendResponse({ success: true });
                break;

            default:
                console.warn('Unknown message from content script:', message.action);
                sendResponse({ error: 'Unknown action' });
        }
    }

    /**
     * Handle messages from background script
     */
    handleBackgroundMessage(message, sendResponse) {
        switch (message.action) {
            case 'lyricsResponse':
                this.handleLyricsResponse(message.data);
                sendResponse({ success: true });
                break;

            case 'settingsUpdated':
                this.handleGlobalSettingsUpdate(message.data);
                sendResponse({ success: true });
                break;

            default:
                console.warn('Unknown message from background script:', message.action);
                sendResponse({ error: 'Unknown action' });
        }
    }

    /**
     * Check if tab is currently active
     */
    isCurrentTab(tabId) {
        return this.musicTabs[this.currentTabIndex]?.id === tabId;
    }

    /**
     * Handle song change from content script
     */
    handleSongChange(songData) {
        if (songData && songData.title && songData.artist) {
            const hasChanged = !this.currentSong ||
                this.currentSong.title !== songData.title ||
                this.currentSong.artist !== songData.artist;

            if (hasChanged) {
                console.log('🎵 Song changed in popup:', songData.title, 'by', songData.artist);
                this.currentSong = songData;
                this.updateSongDisplay();

                // Re-enable controls when we have a valid song
                this.setControlsEnabled(true);

                // Request lyrics from content script (don't fetch independently)
                this.requestLyricsFromContent();
            }
        }
    }

    /**
     * Handle lyrics data change from content script - ONLY update if changed
     */
    handleLyricsDataChange(lyricsData) {
        // Check if lyrics data actually changed
        if (this.lyricsData &&
            this.lyricsData.type === lyricsData.type &&
            this.lyricsData.source === lyricsData.source &&
            JSON.stringify(this.lyricsData.lines) === JSON.stringify(lyricsData.lines) &&
            this.lyricsData.text === lyricsData.text) {
            // No change, skip update
            return;
        }

        console.log('🎵 Lyrics data received in popup:', lyricsData);
        this.lyricsData = lyricsData;
        this.displayLyrics();

        // Lyrics sync will be handled by the dedicated sync interval
    }

    /**
     * SINGLE AUTHORITATIVE TIME UPDATE METHOD - handles ALL time/progress updates
     */
    updateTimeAndProgress(timeData) {
        // Update internal state
        this.currentTime = timeData.currentTime;
        this.duration = timeData.duration || this.duration;

        // Update progress bars
        const progress = this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0;
        this.elements.progressFill.style.width = `${progress}%`;
        this.elements.miniProgressFill.style.width = `${progress}%`;

        // Update time displays
        this.elements.currentTime.textContent = this.formatTime(this.currentTime);
        this.elements.totalTime.textContent = this.formatTime(this.duration);
    }

    /**
     * Handle current time update from content script - REDIRECT to single method
     */
    handleCurrentTimeUpdate(timeData) {
        this.updateTimeAndProgress(timeData);
    }

    /**
     * Handle health check response from content script
     */
    handleHealthCheckResponse(tabId, healthData) {
        const tabIndex = this.musicTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex !== -1) {
            // Update tab health status
            this.musicTabs[tabIndex].lastHealthCheck = Date.now();
            this.musicTabs[tabIndex].isHealthy = true;

            // If this is the current tab, update our state
            if (tabIndex === this.currentTabIndex) {
                if (healthData.song) {
                    this.handleSongChange(healthData.song);
                }
                // REMOVED: updatePlaybackState call to prevent conflicts
                if (healthData.settings) {
                    this.handleContentSettingsChange(healthData.settings);
                }
            }
        }
    }

    /**
     * Perform health check with active tab
     */
    performHealthCheck() {
        if (this.musicTabs.length === 0) return;

        const currentTab = this.musicTabs[this.currentTabIndex];
        if (!currentTab) return;

        // Send health check to current tab
        chrome.tabs.sendMessage(currentTab.id, {
            action: 'healthCheck',
            timestamp: Date.now()
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.warn('Health check failed for tab:', currentTab.id, chrome.runtime.lastError.message);
                this.handleTabConnectionLoss(currentTab.id);
            }
        });
    }

    // REMOVED: syncPlaybackTime() - was causing timing conflicts
    // All time updates now come from content script messages only

    /**
     * Request lyrics from content script
     */
    requestLyricsFromContent() {
        if (this.musicTabs.length === 0) return;

        const currentTab = this.musicTabs[this.currentTabIndex];
        if (!currentTab) return;

        chrome.tabs.sendMessage(currentTab.id, {
            action: 'getLyrics'
        }, (response) => {
            if (response && !chrome.runtime.lastError && response.lyrics) {
                this.handleLyricsDataChange(response.lyrics);
            }
        });
    }

    /**
     * Handle tab connection loss
     */
    handleTabConnectionLoss(tabId) {
        const tabIndex = this.musicTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex !== -1) {
            this.musicTabs[tabIndex].isHealthy = false;
            this.musicTabs[tabIndex].connectionLossCount = (this.musicTabs[tabIndex].connectionLossCount || 0) + 1;

            console.warn(`🔌 Connection lost to tab ${tabId} (${this.musicTabs[tabIndex].connectionLossCount} times)`);

            // If this was the current tab, try to switch to another healthy tab
            if (tabIndex === this.currentTabIndex) {
                this.findAndSwitchToHealthyTab();
            }

            // Try to recover the connection after a delay
            setTimeout(() => {
                this.attemptTabRecovery(tabId);
            }, 2000);
        }
    }

    /**
     * Attempt to recover connection to a tab
     */
    async attemptTabRecovery(tabId) {
        try {
            // Check if tab still exists
            const tab = await chrome.tabs.get(tabId);
            if (!tab || !tab.url || !tab.url.includes('music.youtube.com')) {
                console.log(`🔌 Tab ${tabId} no longer exists or not YouTube Music`);
                this.removeTabFromList(tabId);
                return;
            }

            // Try to send a simple health check
            const response = await this.sendMessageToTab(tabId, 'healthCheck');
            if (response && response.isHealthy) {
                console.log(`✅ Successfully recovered connection to tab ${tabId}`);
                const tabIndex = this.musicTabs.findIndex(t => t.id === tabId);
                if (tabIndex !== -1) {
                    this.musicTabs[tabIndex].isHealthy = true;
                    this.musicTabs[tabIndex].lastHealthCheck = Date.now();
                    this.musicTabs[tabIndex].connectionLossCount = 0;
                }
            }
        } catch (error) {
            console.warn(`🔌 Failed to recover tab ${tabId}:`, error.message);

            // If recovery fails multiple times, remove the tab
            const tabIndex = this.musicTabs.findIndex(t => t.id === tabId);
            if (tabIndex !== -1 && this.musicTabs[tabIndex].connectionLossCount >= 3) {
                console.log(`🗑️ Removing tab ${tabId} after multiple failed recovery attempts`);
                this.removeTabFromList(tabId);
            }
        }
    }

    /**
     * Remove a tab from the music tabs list
     */
    removeTabFromList(tabId) {
        const tabIndex = this.musicTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex !== -1) {
            this.musicTabs.splice(tabIndex, 1);

            // Adjust current tab index if necessary
            if (this.currentTabIndex >= this.musicTabs.length) {
                this.currentTabIndex = Math.max(0, this.musicTabs.length - 1);
            }

            // Update UI
            if (this.musicTabs.length === 0) {
                this.showNoMusicTabsMessage();
            } else {
                this.updateCurrentSong();
            }

            // Update tab navigation visibility
            const hasMultipleTabs = this.musicTabs.length > 1;
            this.elements.prevTab.classList.toggle('visible', hasMultipleTabs);
            this.elements.nextTab.classList.toggle('visible', hasMultipleTabs);
        }
    }

    /**
     * Find and switch to a healthy tab
     */
    findAndSwitchToHealthyTab() {
        const healthyTabIndex = this.musicTabs.findIndex(tab => tab.isHealthy !== false);
        if (healthyTabIndex !== -1 && healthyTabIndex !== this.currentTabIndex) {
            console.log('🔄 Switching to healthy tab:', this.musicTabs[healthyTabIndex].id);
            this.currentTabIndex = healthyTabIndex;
            this.updateCurrentSong();
        } else {
            // No healthy tabs found
            this.showConnectionError();
        }
    }

    /**
     * Handle content script settings change - ONLY update if changed
     */
    handleContentSettingsChange(settings) {
        // Extract synced settings directly (they use the same format now)
        const syncedSettings = {
            SYNC_OFFSET: settings.SYNC_OFFSET,
            AUTO_SCROLL: settings.AUTO_SCROLL,
            FONT_SIZE: settings.FONT_SIZE,
            HIGHLIGHT_COLOR: settings.HIGHLIGHT_COLOR,
            DYNAMIC_THEME_COLORS: settings.DYNAMIC_THEME_COLORS
        };

        // Check if settings actually changed
        let hasChanged = false;
        for (const [key, value] of Object.entries(syncedSettings)) {
            if (this.settings[key] !== value) {
                hasChanged = true;
                break;
            }
        }

        if (!hasChanged) {
            // No change, skip update
            return;
        }

        console.log('🎵 Synced settings from content script:', syncedSettings);

        // Update our settings to match content script
        this.settings = { ...this.settings, ...syncedSettings };

        // Apply theme color if it changed
        if (syncedSettings.HIGHLIGHT_COLOR !== this.currentThemeColor) {
            this.applyThemeColor(syncedSettings.HIGHLIGHT_COLOR);
        }

        // Update UI elements from new settings
        this.updateUIFromSettings();
        this.applyLyricsStyles();
    }

    /**
     * Handle lyrics response from background script
     */
    handleLyricsResponse(lyricsData) {
        // This is for when we fetch lyrics independently (shouldn't happen normally)
        console.log('🎵 Received lyrics from background script');
        this.handleLyricsDataChange(lyricsData);
    }

    /**
     * Handle global settings update from background script
     */
    handleGlobalSettingsUpdate(settings) {
        console.log('🎵 Global settings updated from background');
        this.settings = { ...this.settings, ...settings };
        this.updateUIFromSettings();
        this.saveSettings();
    }

    /**
     * Send command to background script
     */
    sendBackgroundMessage(action, data = {}) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action,
                ...data,
                timestamp: Date.now()
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Send command to content script
     */
    sendContentMessage(action, data = {}) {
        return new Promise((resolve, reject) => {
            if (this.musicTabs.length === 0) {
                reject(new Error('No music tabs available'));
                return;
            }

            const currentTab = this.musicTabs[this.currentTabIndex];
            if (!currentTab) {
                reject(new Error('No current tab selected'));
                return;
            }

            chrome.tabs.sendMessage(currentTab.id, {
                action,
                data,
                timestamp: Date.now()
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Sync settings with background script
     */
    async syncSettingsWithBackground() {
        try {
            await this.sendBackgroundMessage('updateSettings', {
                settings: this.settings,
                extensionEnabled: this.extensionEnabled,
                themeColor: this.currentThemeColor
            });
        } catch (error) {
            console.error('❌ Failed to sync settings with background:', error);
        }
    }

    /**
     * Display lyrics in the container - EXACT copy from content.js logic
     */
    displayLyrics() {
        try {
            console.log('🎵 Displaying lyrics:', this.lyricsData);

            // Validate lyrics data
            if (!this.lyricsData) {
                console.log('❌ No lyrics data provided');
                this.showLyricsError('Invalid lyrics data received.');
                return;
            }

            // Hide loading and error states
            this.elements.lyricsLoading.style.display = 'none';
            this.elements.lyricsError.style.display = 'none';

            // Get the source for footer
            const source = this.lyricsData.source || 'Unknown';
            const sourceText = `Source: ${source}`;

            if (this.lyricsData.type === 'synced') {
                if (!this.lyricsData.lines || !Array.isArray(this.lyricsData.lines) || this.lyricsData.lines.length === 0) {
                    console.log('❌ Invalid synced lyrics data');
                    this.showLyricsError('Invalid synced lyrics format received.');
                    return;
                }

                console.log('🎵 Displaying synced lyrics with', this.lyricsData.lines.length, 'lines');

                // Display synchronized lyrics with error handling for malformed lines
                const validLines = this.lyricsData.lines.filter(line =>
                    line && typeof line.time === 'number' && typeof line.text === 'string'
                );

                if (validLines.length === 0) {
                    console.log('❌ No valid synced lyrics lines found');
                    this.showLyricsError('Synced lyrics data is corrupted.');
                    return;
                }

                // Use the lyrics lines container (EXACT same as content.js)
                this.elements.lyricsLinesContainer.innerHTML = validLines.map((line, index) =>
                    `<div class="ytmusic-lyrics-line" data-time="${line.time}" data-index="${index}" title="Go to ${this.formatTime(line.time)}">
                        ${this.escapeHtml(line.text)}
                    </div>`
                ).join('');

                // Add click-to-seek functionality for synced lyrics
                this.setupClickToSeek();

                // Update source with settings button for synced lyrics (simplified for popup)
                this.elements.ytmusicLyricsSource.innerHTML = `
                    <span>${sourceText}</span>
                `;

                // Start synchronization for synced lyrics
                console.log('🎵 Starting lyrics synchronization');
                this.startSynchronization();

            } else if (this.lyricsData.type === 'static') {
                if (!this.lyricsData.text || typeof this.lyricsData.text !== 'string') {
                    console.log('❌ Invalid static lyrics data');
                    this.showLyricsError('Invalid static lyrics format received.');
                    return;
                }

                console.log('🎵 Displaying static lyrics');
                // Display static lyrics with HTML escaping (EXACT same as content.js)
                this.elements.lyricsLinesContainer.innerHTML = `<div class="ytmusic-static-lyrics">${this.escapeHtml(this.lyricsData.text)}</div>`;

                // Note: Static lyrics don't have timing data, so no click-to-seek functionality

                // Update source separately
                this.elements.ytmusicLyricsSource.textContent = sourceText;
            } else {
                console.log('❌ Unknown lyrics type:', this.lyricsData.type);
                this.showLyricsError('Unknown lyrics format received.');
            }
        } catch (error) {
            console.error('❌ Error displaying lyrics:', error);
            this.showLyricsError('Error displaying lyrics. Please try refreshing the page.');
        }
    }

    /**
     * Escape HTML to prevent XSS (same as content.js)
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Set up click-to-seek functionality for lyrics lines - EXACT copy from content.js
     * @private
     */
    setupClickToSeek() {
        if (!this.elements.lyricsLinesContainer) return;

        const lyricsLines = this.elements.lyricsLinesContainer.querySelectorAll('.ytmusic-lyrics-line[data-time]');

        lyricsLines.forEach(line => {
            line.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const targetTime = parseFloat(line.dataset.time);
                if (!isNaN(targetTime)) {
                    // Apply both the early show offset and user's sync offset setting
                    const LYRICS_EARLY_SHOW = 0.5; // Same as content.js
                    const seekTime = (targetTime - LYRICS_EARLY_SHOW) + 0.1 + (this.settings.syncOffset || 0);
                    this.seekToTime(Math.max(0, seekTime)); // Don't seek to negative time
                }
            });
        });

        console.log(`🎵 Click-to-seek enabled for ${lyricsLines.length} lyrics lines`);
    }

    /**
     * Highlight current lyrics line and auto-scroll - EXACT copy from content.js
     * @param {number} currentTime - Current playback time in seconds
     * @private
     */
    highlightCurrentLine(currentTime) {
        if (!this.elements.lyricsLinesContainer) { return; }

        const lines = this.elements.lyricsLinesContainer.querySelectorAll('.ytmusic-lyrics-line');
        let activeIndex = -1;

        // Simple timing calculation: current time + user sync offset
        const adjustedTime = currentTime + (this.settings.syncOffset || 0);

        // Find the current line - show lyrics LYRICS_EARLY_SHOW seconds before their timestamp
        const LYRICS_EARLY_SHOW = 0.5; // Same as content.js
        for (let i = 0; i < lines.length; i++) {
            const lineTime = parseFloat(lines[i].dataset.time);

            // Show this line if current time is within LYRICS_EARLY_SHOW seconds of the line time
            if (adjustedTime >= (lineTime - LYRICS_EARLY_SHOW)) {
                activeIndex = i;
            } else {
                break;
            }
        }

        // Remove previous highlighting
        lines.forEach((line, index) => {
            line.classList.remove('active', 'passed', 'upcoming');

            // Add appropriate class based on position relative to current line
            if (activeIndex >= 0) {
                if (index < activeIndex) {
                    line.classList.add('passed');
                } else if (index === activeIndex) {
                    line.classList.add('active');
                } else if (index === activeIndex + 1) {
                    line.classList.add('upcoming');
                }
            }
        });

        // Highlight current line and scroll
        if (activeIndex >= 0) {
            // Log current line for debugging (only when it changes and throttled)
            if (this.lastActiveIndex !== activeIndex) {
                const now = Date.now();
                if (!this.lastLineLogTime || now - this.lastLineLogTime > 1000) { // Throttle to max 1 log per second
                    const lineText = lines[activeIndex].textContent.trim();
                    const lineTime = parseFloat(lines[activeIndex].dataset.time);
                    console.log(`🎤 Current line [${lineTime}s]: "${lineText}" (playback: ${currentTime.toFixed(1)}s)`);
                    this.lastLineLogTime = now;
                }
                this.lastActiveIndex = activeIndex;
            }

            // Auto-scroll to current line (now always centers)
            this.scrollToLine(lines[activeIndex]);
        } else if (currentTime > 0) {
            // Log when no line is active but music is playing (throttled)
            if (this.lastActiveIndex !== -1) {
                const now = Date.now();
                if (!this.lastLineLogTime || now - this.lastLineLogTime > 2000) { // Throttle waiting message
                    console.log(`🎵 Waiting for first line (playback: ${currentTime.toFixed(1)}s)`);
                    this.lastLineLogTime = now;
                }
                this.lastActiveIndex = -1;
            }
        }
    }

    /**
     * Update lyrics synchronization - wrapper for highlightCurrentLine
     */
    updateLyricsSync() {
        if (!this.lyricsData || this.lyricsData.type !== 'synced') return;
        this.highlightCurrentLine(this.currentTime);
    }

    /**
     * Scroll to the active lyrics line - EXACT copy from content.js
     * @param {HTMLElement} element - The lyrics line element to scroll to
    
     * @private
     */
    scrollToLine(element) {
        if (!this.elements.lyricsLinesContainer) { return; }

        // Don't auto-scroll if user is manually scrolling
        if (this.isUserScrolling) {
            return;
        }

        // Don't auto-scroll if setting is disabled
        if (!this.settings.autoScroll) {
            return;
        }

        // Mark as programmatic scroll to prevent user scroll detection
        this.programmaticScroll = true;

        // Always center the active lyric in the container
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        // Clear programmatic scroll flag after a short delay
        setTimeout(() => {
            this.programmaticScroll = false;
        }, 300); // Same delay as content.js
    }

    /**
     * Start lyrics synchronization - uses existing time data, NO additional requests
     * @private
     */
    startSynchronization() {
        if (!this.lyricsData || this.lyricsData.type !== 'synced') {
            console.log('❌ Cannot start sync: no synced lyrics data');
            return;
        }

        this.stopSynchronization();
        console.log('🎵 Starting synchronization with', this.lyricsData.lines.length, 'lines');

        // Use existing time data - NO additional time requests to prevent conflicts
        this.syncInterval = setInterval(() => {
            if (!this.isPlaying || !this.currentTime) return;

            // Use the existing currentTime that's updated by the single authoritative method
            this.highlightCurrentLine(this.currentTime);
        }, 100); // 100ms for smooth lyrics sync
    }

    /**
     * Stop lyrics synchronization - EXACT copy from content.js
     */
    stopSynchronization() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }

    /**
     * Format time in seconds to MM:SS format - EXACT copy from content.js
     * @param {number} seconds - Time in seconds
     * @returns {string} Formatted time string
     * @private
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else if (minutes > 0) {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `0:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Escape HTML to prevent XSS - EXACT copy from content.js
     */
    escapeHtml(text) {
        if (!text) { return ''; }
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Handle progress bar click for seeking
     */
    handleProgressBarClick(event) {
        if (!this.duration || this.duration <= 0) return;

        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const progressWidth = rect.width;
        const clickPercentage = clickX / progressWidth;
        const seekTime = clickPercentage * this.duration;

        // Clamp to valid range
        const clampedTime = Math.max(0, Math.min(seekTime, this.duration));

        console.log(`🎵 Progress bar clicked: ${clickPercentage.toFixed(2)} -> ${clampedTime.toFixed(1)}s`);
        this.seekToTime(clampedTime);
    }

    /**
     * Seek to specific time in the song
     */
    async seekToTime(time) {
        try {
            await this.sendContentMessage('seekTo', { time });
            console.log('✅ Seek command sent:', time);

            // Update current time immediately for responsive UI
            this.currentTime = time;
            this.handleCurrentTimeUpdate({ currentTime: time, duration: this.duration });
        } catch (error) {
            console.error('❌ Failed to seek:', error);
            this.showError('Failed to seek to position');
        }
    }

    /**
     * Extract and apply theme color from album cover
     */
    async extractAndApplyThemeColor() {
        if (!this.currentSong?.albumCover || !this.settings.dynamicColors) return;

        try {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = () => {
                // Resize canvas to image size for better color sampling
                this.colorExtractionCanvas.width = img.width;
                this.colorExtractionCanvas.height = img.height;

                // Draw image to canvas
                this.colorExtractionContext.drawImage(img, 0, 0);

                // Extract dominant color
                const color = this.extractDominantColor();
                if (color && color !== this.currentThemeColor) {
                    this.animateThemeColorChange(color);
                    this.saveSettings();
                }
            };

            img.onerror = () => {
                console.warn('Could not load album cover for color extraction');
            };

            img.src = this.currentSong.albumCover;

        } catch (error) {
            console.error('❌ Error extracting theme color:', error);
        }
    }

    /**
     * Extract dominant color from canvas
     */
    extractDominantColor() {
        try {
            const imageData = this.colorExtractionContext.getImageData(
                0, 0, this.colorExtractionCanvas.width, this.colorExtractionCanvas.height
            );

            const data = imageData.data;
            const colorCounts = {};

            // Sample every 4th pixel for performance
            for (let i = 0; i < data.length; i += 16) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const a = data[i + 3];

                // Skip transparent pixels and very dark/light colors
                if (a < 128 || (r + g + b) < 50 || (r + g + b) > 650) continue;

                // Quantize colors to reduce noise
                const qr = Math.round(r / 32) * 32;
                const qg = Math.round(g / 32) * 32;
                const qb = Math.round(b / 32) * 32;

                const colorKey = `${qr},${qg},${qb}`;
                colorCounts[colorKey] = (colorCounts[colorKey] || 0) + 1;
            }

            // Find most common color
            let maxCount = 0;
            let dominantColor = null;

            for (const [color, count] of Object.entries(colorCounts)) {
                if (count > maxCount) {
                    maxCount = count;
                    dominantColor = color;
                }
            }

            if (dominantColor) {
                const [r, g, b] = dominantColor.split(',').map(Number);
                return this.rgbToHex(r, g, b);
            }

            return null;

        } catch (error) {
            console.error('❌ Error extracting dominant color:', error);
            return null;
        }
    }

    /**
     * Start simplified update loop for the popup
     */
    startUpdateLoop() {
        // Clear any existing intervals
        this.clearAllIntervals();

        // Main update interval for health checks only (reduced frequency)
        this.updateInterval = setInterval(() => {
            this.performHealthCheck();
        }, POPUP_CONFIG.TIMING.UPDATE_INTERVAL);

        // Sync check interval to ensure we're still connected to content script
        this.syncCheckInterval = setInterval(() => {
            this.checkContentScriptSync();
        }, POPUP_CONFIG.TIMING.SYNC_CHECK_INTERVAL);

        console.log('🔄 Simplified update loops started');
    }

    /**
     * Check if we're still synced with content script
     */
    async checkContentScriptSync() {
        try {
            if (this.musicTabs.length === 0) return;

            const currentTab = this.musicTabs[this.currentTabIndex];
            if (!currentTab) return;

            // Send a quick health check to ensure content script is responsive
            const response = await this.sendMessageToTab(currentTab.id, 'healthCheck');
            if (response && response.song) {
                // Update song info if it changed
                if (!this.currentSong ||
                    this.currentSong.title !== response.song.title ||
                    this.currentSong.artist !== response.song.artist) {
                    this.handleSongChange(response.song);
                }
            }
        } catch (error) {
            console.warn('❌ Sync check failed:', error);
            // Try to refresh tabs if sync is lost
            this.refreshTabs();
        }
    }

    /**
     * Clear all intervals
     */
    clearAllIntervals() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        if (this.lyricsInterval) {
            clearInterval(this.lyricsInterval);
            this.lyricsInterval = null;
        }
        if (this.syncCheckInterval) {
            clearInterval(this.syncCheckInterval);
            this.syncCheckInterval = null;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌', message);
        // Could implement toast notifications here
    }

    /**
     * Show no music tabs message
     */
    showNoMusicTabsMessage() {
        this.elements.songTitle.textContent = 'No YouTube Music tabs found';
        this.elements.songArtist.textContent = 'Open YouTube Music to start';
        this.elements.miniSongTitle.textContent = 'No YouTube Music tabs found';
        this.elements.miniSongArtist.textContent = 'Open YouTube Music to start';
    }

    /**
     * Show connection error message
     */
    showConnectionError() {
        this.elements.songTitle.textContent = 'Connection error';
        this.elements.songArtist.textContent = 'Trying to reconnect...';
        this.elements.miniSongTitle.textContent = 'Connection error';
        this.elements.miniSongArtist.textContent = 'Trying to reconnect...';

        // Hide playback controls during connection error
        this.setControlsEnabled(false);

        // Try to refresh tabs after a delay
        setTimeout(() => {
            this.refreshTabs();
        }, 3000);
    }

    /**
     * Enable/disable playback controls
     */
    setControlsEnabled(enabled) {
        const controls = [
            this.elements.prevButton,
            this.elements.playPauseButton,
            this.elements.nextButton,
            this.elements.miniPrevButton,
            this.elements.miniPlayPauseButton,
            this.elements.miniNextButton,
            this.elements.progressBar,
            this.elements.miniProgressBar
        ];

        controls.forEach(control => {
            if (control) {
                control.style.pointerEvents = enabled ? 'auto' : 'none';
                control.style.opacity = enabled ? '1' : '0.5';
            }
        });
    }

    /**
     * Show error message with retry option
     */
    showError(message, canRetry = false) {
        console.error('❌ Popup error:', message);

        // Show error in song display
        this.elements.songTitle.textContent = 'Error';
        this.elements.songArtist.textContent = message;
        this.elements.miniSongTitle.textContent = 'Error';
        this.elements.miniSongArtist.textContent = message;

        if (canRetry) {
            // Add retry functionality
            setTimeout(() => {
                this.refreshTabs();
            }, 5000);
        }
    }

    /**
     * Show no song message
     */
    showNoSongMessage() {
        this.elements.songTitle.textContent = 'No song detected';
        this.elements.songArtist.textContent = 'Play a song to start';
        this.elements.miniSongTitle.textContent = 'No song detected';
        this.elements.miniSongArtist.textContent = 'Play a song to start';
    }

    /**
     * Show lyrics error - updated for new element structure
     */
    showLyricsError(message) {
        // Hide loading state and clear lyrics
        this.elements.lyricsLoading.style.display = 'none';
        this.elements.lyricsLinesContainer.innerHTML = '';

        // Show error
        this.elements.lyricsError.style.display = 'block';
        this.elements.lyricsError.querySelector('.error-text').textContent = message;

        // Update source
        this.elements.ytmusicLyricsSource.textContent = 'Error loading lyrics';
    }

    /**
     * Format time in MM:SS format
     */
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * Convert hex color to RGB
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    /**
     * Convert RGB to hex
     */
    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    /**
     * Lighten a hex color
     */
    lightenColor(hex, percent) {
        const rgb = this.hexToRgb(hex);
        if (!rgb) return hex;

        const { r, g, b } = rgb;
        const factor = 1 + (percent / 100);

        return this.rgbToHex(
            Math.min(255, Math.round(r * factor)),
            Math.min(255, Math.round(g * factor)),
            Math.min(255, Math.round(b * factor))
        );
    }

    /**
     * Darken a hex color
     */
    darkenColor(hex, percent) {
        const rgb = this.hexToRgb(hex);
        if (!rgb) return hex;

        const { r, g, b } = rgb;
        const factor = 1 - (percent / 100);

        return this.rgbToHex(
            Math.max(0, Math.round(r * factor)),
            Math.max(0, Math.round(g * factor)),
            Math.max(0, Math.round(b * factor))
        );
    }

    /**
     * Export settings to JSON file
     */
    exportSettings() {
        const settingsData = {
            version: '2.0.0',
            timestamp: new Date().toISOString(),
            settings: this.settings,
            themeColor: this.currentThemeColor,
            extensionEnabled: this.extensionEnabled
        };

        const blob = new Blob([JSON.stringify(settingsData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ytmusic-lyrics-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * Import settings from JSON file
     */
    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (event) => {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const settingsData = JSON.parse(e.target.result);

                    if (settingsData.settings) {
                        this.settings = { ...POPUP_CONFIG.DEFAULTS, ...settingsData.settings };
                    }

                    if (settingsData.themeColor) {
                        this.currentThemeColor = settingsData.themeColor;
                        this.applyThemeColor(this.currentThemeColor);
                    }

                    if (typeof settingsData.extensionEnabled === 'boolean') {
                        this.extensionEnabled = settingsData.extensionEnabled;
                    }

                    this.updateUIFromSettings();
                    this.saveSettings();

                    console.log('✅ Settings imported successfully');

                } catch (error) {
                    console.error('❌ Error importing settings:', error);
                    this.showError('Invalid settings file');
                }
            };

            reader.readAsText(file);
        };

        input.click();
    }

    /**
     * Reset all settings to defaults
     */
    resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
            this.settings = { ...POPUP_CONFIG.DEFAULTS };
            this.currentThemeColor = POPUP_CONFIG.THEME.DEFAULT_COLOR;
            this.extensionEnabled = true;

            this.applyThemeColor(this.currentThemeColor);
            this.updateUIFromSettings();
            this.saveSettings();

            console.log('✅ Settings reset to defaults');
        }
    }

    /**
     * Clear lyrics cache
     */
    clearLyricsCache() {
        if (confirm('Are you sure you want to clear the lyrics cache? This will remove all cached lyrics.')) {
            chrome.storage.local.clear(() => {
                if (chrome.runtime.lastError) {
                    console.error('❌ Error clearing cache:', chrome.runtime.lastError);
                } else {
                    console.log('✅ Lyrics cache cleared');
                    // Show some feedback to user
                    this.showTemporaryMessage('Cache cleared successfully');
                }
            });
        }
    }

    /**
     * Open GitHub repository page
     */
    openGitHubPage() {
        chrome.tabs.create({
            url: 'https://github.com/sukarth/liveytmusiclyrics'
        });
    }

    /**
     * Report an issue
     */
    reportIssue() {
        chrome.tabs.create({
            url: 'https://github.com/sukarth/liveytmusiclyrics/issues/new'
        });
    }

    /**
     * Show temporary message to user with toast notification
     */
    showTemporaryMessage(message, type = 'info') {
        this.showToast(message, type);
        console.log('📢', message);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 3000) {
        // Create toast container if it doesn't exist
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        // Add to container
        container.appendChild(toast);

        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Auto-remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * Show loading overlay
     */
    showLoadingOverlay(message = 'Loading...') {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div style="text-align: center;">
                <div class="loading-spinner"></div>
                <div style="margin-top: 16px; color: rgba(255, 255, 255, 0.8);">${message}</div>
            </div>
        `;

        document.body.appendChild(overlay);
        return overlay;
    }

    /**
     * Hide loading overlay
     */
    hideLoadingOverlay(overlay) {
        if (overlay && overlay.parentNode) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }

    /**
     * Show error state with retry option
     */
    showErrorState(container, message, retryCallback) {
        container.innerHTML = `
            <div class="error-state">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
                </svg>
                <h3>Something went wrong</h3>
                <p>${message}</p>
                ${retryCallback ? '<button class="retry-button">Try Again</button>' : ''}
            </div>
        `;

        if (retryCallback) {
            const retryButton = container.querySelector('.retry-button');
            retryButton.addEventListener('click', retryCallback);
        }
    }

    /**
     * Enhanced error handling with user-friendly messages
     */
    handleError(error, context = '') {
        console.error(`❌ Error in ${context}:`, error);

        let userMessage = 'An unexpected error occurred';

        if (error.message) {
            if (error.message.includes('network') || error.message.includes('fetch')) {
                userMessage = 'Network connection error. Please check your internet connection.';
            } else if (error.message.includes('timeout')) {
                userMessage = 'Request timed out. Please try again.';
            } else if (error.message.includes('permission')) {
                userMessage = 'Permission denied. Please check extension permissions.';
            }
        }

        this.showToast(userMessage, 'error', 5000);
        return userMessage;
    }

    /**
     * Validate popup state and show appropriate UI
     */
    validateAndUpdateUI() {
        // Check if extension is enabled
        if (!this.extensionEnabled) {
            this.showDisabledState();
            return;
        }

        // Check if there are music tabs
        if (this.musicTabs.length === 0) {
            this.showNoTabsState();
            return;
        }

        // Check if current song is available
        if (!this.currentSong) {
            this.showNoSongState();
            return;
        }

        // All good, show normal state
        this.showNormalState();
    }

    /**
     * Show disabled state
     */
    showDisabledState() {
        this.elements.nowPlaying.innerHTML = `
            <div class="error-state">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
                <h3>Extension Disabled</h3>
                <p>Enable the extension using the toggle in the header to start using lyrics.</p>
            </div>
        `;
    }

    /**
     * Show no tabs state
     */
    showNoTabsState() {
        this.elements.nowPlaying.innerHTML = `
            <div class="error-state">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
                </svg>
                <h3>No YouTube Music Found</h3>
                <p>Open YouTube Music in a tab to start viewing lyrics.</p>
                <button class="retry-button" onclick="window.open('https://music.youtube.com', '_blank')">Open YouTube Music</button>
            </div>
        `;
    }

    /**
     * Show no song state
     */
    showNoSongState() {
        // This is handled by the existing updateSongDisplay method
        this.showNoSongMessage();
    }

    /**
     * Show normal state
     */
    showNormalState() {
        // Restore normal UI if it was in an error state
        if (this.elements.nowPlaying.querySelector('.error-state')) {
            location.reload(); // Simple way to restore normal UI
        }
    }

    /**
     * Add ripple effects to buttons
     */
    addRippleEffects() {
        const buttons = document.querySelectorAll('.header-button, .control-button, .mini-control-button, .settings-button');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRipple(e, button);
            });
        });
    }

    /**
     * Create ripple effect on button click
     */
    createRipple(event, button) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');

        button.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    /**
     * Animate element entrance
     */
    animateElementEntrance(element, delay = 0) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';

        setTimeout(() => {
            element.style.transition = 'all 0.4s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, delay);
    }

    /**
     * Animate lyrics line appearance
     */
    animateLyricsLine(lineElement, index) {
        lineElement.style.opacity = '0';
        lineElement.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            lineElement.style.transition = 'all 0.3s ease-out';
            lineElement.style.opacity = '0.7';
            lineElement.style.transform = 'translateX(0)';
        }, index * 50); // Stagger the animations
    }

    /**
     * Enhanced minimize animation
     */
    minimizePlayerWithAnimation() {
        if (this.isMinimized) return;

        // Add scale animation to the main view
        this.elements.nowPlaying.style.transform = 'scale(0.95)';
        this.elements.nowPlaying.style.opacity = '0.8';

        setTimeout(() => {
            this.minimizePlayer();

            // Reset the transform after animation
            setTimeout(() => {
                this.elements.nowPlaying.style.transform = '';
                this.elements.nowPlaying.style.opacity = '';
            }, POPUP_CONFIG.TIMING.TRANSITION_DURATION);
        }, 150);
    }

    /**
     * Enhanced expand animation
     */
    expandPlayerWithAnimation() {
        if (!this.isMinimized) return;

        // Pre-animate the main view
        this.elements.nowPlaying.style.transform = 'scale(1.05)';
        this.elements.nowPlaying.style.opacity = '0.8';

        this.expandPlayer();

        // Animate back to normal
        setTimeout(() => {
            this.elements.nowPlaying.style.transition = 'all 0.3s ease-out';
            this.elements.nowPlaying.style.transform = 'scale(1)';
            this.elements.nowPlaying.style.opacity = '1';

            // Reset transition
            setTimeout(() => {
                this.elements.nowPlaying.style.transition = '';
            }, 300);
        }, 50);
    }

    /**
     * Animate theme color change
     */
    animateThemeColorChange(newColor) {
        // Create a smooth transition overlay
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.background = newColor;
        overlay.style.opacity = '0';
        overlay.style.pointerEvents = 'none';
        overlay.style.zIndex = '9999';
        overlay.style.transition = 'opacity 0.3s ease';

        document.body.appendChild(overlay);

        // Fade in overlay
        setTimeout(() => {
            overlay.style.opacity = '0.1';
        }, 10);

        // Apply new theme color
        setTimeout(() => {
            this.applyThemeColor(newColor);
        }, 150);

        // Fade out overlay
        setTimeout(() => {
            overlay.style.opacity = '0';
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }, 200);
    }

    /**
     * Cleanup when popup is closed
     */
    cleanup() {
        // Clear all intervals
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        if (this.lyricsInterval) {
            clearInterval(this.lyricsInterval);
        }

        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }

        if (this.playbackSyncInterval) {
            clearInterval(this.playbackSyncInterval);
        }

        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        console.log('🎵 Popup cleanup completed');
    }

    /**
     * Add accessibility features
     */
    addAccessibilityFeatures() {
        // Add ARIA labels to buttons without text
        const buttons = document.querySelectorAll('button:not([aria-label])');
        buttons.forEach(button => {
            const title = button.getAttribute('title');
            if (title) {
                button.setAttribute('aria-label', title);
            }
        });

        // Add role attributes to interactive elements
        document.querySelectorAll('.lyrics-line').forEach(line => {
            line.setAttribute('role', 'button');
            line.setAttribute('tabindex', '0');
        });

        // Add keyboard navigation for lyrics
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('lyrics-line')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.target.click();
                }
            }
        });

        // Add focus management
        this.addFocusManagement();
    }

    /**
     * Add focus management for better keyboard navigation
     */
    addFocusManagement() {
        // Focus trap for settings modal
        const settingsPage = this.elements.settingsPage;
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

        settingsPage.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const focusableContent = settingsPage.querySelectorAll(focusableElements);
                const firstFocusable = focusableContent[0];
                const lastFocusable = focusableContent[focusableContent.length - 1];

                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        lastFocusable.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        firstFocusable.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }

    /**
     * Add performance optimizations
     */
    addPerformanceOptimizations() {
        // Skip resize handling for popups to prevent height jumping
        // CSS media queries handle responsive design properly

        // Use Intersection Observer for lyrics visibility
        this.setupLyricsVisibilityObserver();

        // Optimize image loading
        this.optimizeImageLoading();
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Only adjust layout for very small screens
        const width = window.innerWidth;

        // Don't resize the popup body - let CSS handle it
        if (width < 350) {
            console.log('Very small screen detected, adjusting layout');
            // Only make minimal adjustments for very small screens
            if (this.elements.lyricsContainer) {
                this.elements.lyricsContainer.style.height = '250px';
            }
        }

        // Don't dynamically change popup dimensions - causes height jumping
        // The CSS media queries handle responsive design properly
    }

    /**
     * Set up Intersection Observer for lyrics visibility
     */
    setupLyricsVisibilityObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '20px'
            });

            // Observe lyrics lines when they're added
            const originalDisplayLyrics = this.displayLyrics.bind(this);
            this.displayLyrics = function () {
                originalDisplayLyrics();

                // Observe new lyrics lines
                this.elements.lyricsLines.querySelectorAll('.lyrics-line').forEach(line => {
                    observer.observe(line);
                });
            };
        }
    }

    /**
     * Optimize image loading
     */
    optimizeImageLoading() {
        // Add loading="lazy" to images
        document.querySelectorAll('img').forEach(img => {
            img.loading = 'lazy';
        });

        // Preload next album cover when switching tabs
        if (this.musicTabs.length > 1) {
            const nextTabIndex = (this.currentTabIndex + 1) % this.musicTabs.length;
            const nextTab = this.musicTabs[nextTabIndex];

            if (nextTab) {
                chrome.tabs.sendMessage(nextTab.id, { action: 'preloadAlbumCover' });
            }
        }
    }

    /**
     * Add final polish and micro-interactions
     */
    addFinalPolish() {
        // Add subtle hover effects to interactive elements
        document.querySelectorAll('.lyrics-line').forEach(line => {
            line.addEventListener('mouseenter', () => {
                if (!line.classList.contains('active')) {
                    line.style.transform = 'translateX(2px)';
                }
            });

            line.addEventListener('mouseleave', () => {
                if (!line.classList.contains('active')) {
                    line.style.transform = '';
                }
            });
        });

        // Add smooth scrolling to lyrics container
        this.elements.lyricsContent.style.scrollBehavior = 'smooth';

        // Add visual feedback for theme color changes
        this.addThemeColorFeedback();
    }

    /**
     * Add visual feedback for theme color changes
     */
    addThemeColorFeedback() {
        const originalApplyThemeColor = this.applyThemeColor.bind(this);
        this.applyThemeColor = function (color) {
            // Add a subtle flash effect
            const flash = document.createElement('div');
            flash.style.position = 'fixed';
            flash.style.top = '0';
            flash.style.left = '0';
            flash.style.width = '100%';
            flash.style.height = '100%';
            flash.style.background = color;
            flash.style.opacity = '0';
            flash.style.pointerEvents = 'none';
            flash.style.zIndex = '9999';
            flash.style.transition = 'opacity 0.3s ease';

            document.body.appendChild(flash);

            // Flash effect
            setTimeout(() => {
                flash.style.opacity = '0.1';
            }, 10);

            setTimeout(() => {
                flash.style.opacity = '0';
                setTimeout(() => {
                    if (flash.parentNode) {
                        flash.parentNode.removeChild(flash);
                    }
                }, 200);
            }, 100);

            // Apply the actual color change
            originalApplyThemeColor(color);
        };
    }

    /**
     * Show welcome message for first-time users
     */
    async showWelcomeMessageIfNeeded() {
        try {
            const result = await chrome.storage.local.get(['hasSeenWelcome']);

            if (!result.hasSeenWelcome) {
                setTimeout(() => {
                    this.showToast('Welcome to Live YT Music Lyrics! 🎵', 'success', 5000);

                    setTimeout(() => {
                        this.showToast('Tip: Use keyboard shortcuts for quick navigation', 'info', 4000);
                    }, 2000);
                }, 1000);

                // Mark as seen
                await chrome.storage.local.set({ hasSeenWelcome: true });
            }
        } catch (error) {
            console.warn('Could not check welcome status:', error);
        }
    }

    /**
     * Add advanced error recovery
     */
    addErrorRecovery() {
        // Auto-retry failed operations
        window.addEventListener('error', (event) => {
            console.error('Global error caught:', event.error);

            // Try to recover from common errors
            if (event.error?.message?.includes('Extension context invalidated')) {
                this.showToast('Extension updated. Please refresh the page.', 'warning', 8000);
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);

            // Prevent the default browser error handling
            event.preventDefault();

            // Show user-friendly error
            this.handleError(event.reason, 'background operation');
        });
    }

    /**
     * Add performance monitoring
     */
    addPerformanceMonitoring() {
        // Monitor memory usage
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB threshold
                    console.warn('High memory usage detected:', memory);
                    this.optimizeMemoryUsage();
                }
            }, 30000); // Check every 30 seconds
        }

        // Frame rate monitoring disabled to reduce console spam
        // Performance optimization will be triggered by other means if needed
    }

    /**
     * Optimize memory usage
     */
    optimizeMemoryUsage() {
        // Clear old lyrics data
        if (this.lyricsData && this.lyricsData.lines?.length > 100) {
            console.log('Optimizing lyrics data...');
            // Keep only visible lyrics
            const visibleLines = this.elements.lyricsLines.querySelectorAll('.lyrics-line');
            if (visibleLines.length < this.lyricsData.lines.length) {
                this.lyricsData.lines = Array.from(visibleLines).map(line => ({
                    text: line.textContent,
                    time: parseFloat(line.dataset.time)
                }));
            }
        }

        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    /**
     * Optimize performance
     */
    optimizePerformance() {
        // Reduce animation complexity
        document.documentElement.style.setProperty('--transition-fast', '0.1s');
        document.documentElement.style.setProperty('--transition-medium', '0.2s');

        // Disable non-essential animations
        document.querySelectorAll('.lyrics-line').forEach(line => {
            line.style.transition = 'color 0.1s ease';
        });

        console.log('Performance optimizations applied');
    }

    /**
     * Add final validation and health checks
     */
    validatePopupHealth() {
        const issues = [];

        // Check if all required elements exist
        const requiredElements = [
            'settingsButton', 'masterToggle', 'songTitle', 'songArtist',
            'lyricsContainer', 'settingsPage'
        ];

        requiredElements.forEach(elementKey => {
            if (!this.elements[elementKey]) {
                issues.push(`Missing element: ${elementKey}`);
            }
        });

        // Check if settings are valid
        if (!this.settings || typeof this.settings !== 'object') {
            issues.push('Invalid settings object');
        }

        // Check if theme color is valid
        if (!this.currentThemeColor || !this.currentThemeColor.match(/^#[0-9A-F]{6}$/i)) {
            issues.push('Invalid theme color');
        }

        if (issues.length > 0) {
            console.warn('Popup health issues detected:', issues);
            this.showToast('Some features may not work correctly', 'warning', 3000);
        } else {
            console.log('✅ Popup health check passed');
        }

        return issues.length === 0;
    }

    /**
     * Find tab ID from message for direct content script messages
     * This handles cases where sender.tab is not available
     */
    findTabIdFromMessage(message) {
        // Check if we have any active tabs
        if (this.musicTabs.length === 0) {
            return null;
        }

        // Use the source tab ID sent by the content script to find the matching tab
        if (message.sourceTabId) {
            for (const tab of this.musicTabs) {
                // Try to match with a known tab
                if (tab.sourceTabId === message.sourceTabId) {
                    return tab.id;
                }
            }
        }

        // If we can't identify the specific tab, just use the current active tab
        return this.musicTabs[this.currentTabIndex]?.id;
    }
}

// ============================================================================
// INITIALIZATION
// ============================================================================

/**
 * Initialize the new popup controller when DOM is ready
 */
function initializePopup() {
    try {
        const popup = new PopupController();
        console.log('🎵 Popup Controller initialized');

        // Make available for debugging
        if (typeof window !== 'undefined') {
            window.popupController = popup;
        }

        // Cleanup on window unload
        window.addEventListener('beforeunload', () => {
            if (popup.coordinatorPort) {
                popup.coordinatorPort.disconnect();
            }
            if (popup.lyricsInterval) {
                clearInterval(popup.lyricsInterval);
            }
            if (popup.heartbeatCheckInterval) {
                clearInterval(popup.heartbeatCheckInterval);
            }
        });

    } catch (error) {
        console.error('❌ Failed to initialize popup:', error);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopup);
} else {
    initializePopup();
}
